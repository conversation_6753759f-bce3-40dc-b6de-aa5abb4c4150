#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32S3 摔倒检测详细测试脚本
功能完整的实时监控、数据分析和测试工具
作者: ESP32S3 AiBox项目组
版本: 2.0
"""

import json
import time
import threading
import os
import sys
import csv
import statistics
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import paho.mqtt.client as mqtt
import winsound  # Windows系统声音提示
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from collections import deque
import queue

class FallDetectionTester:
    def __init__(self):
        # 加载配置
        self.load_config()

        # 数据存储
        self.imu_data = {}
        self.fall_count = 0
        self.false_positive_count = 0
        self.test_start_time = datetime.now()
        self.data_log = []
        self.real_time_data = {
            'timestamps': deque(maxlen=500),
            'acc_x': deque(maxlen=500),
            'acc_y': deque(maxlen=500),
            'acc_z': deque(maxlen=500),
            'gyr_x': deque(maxlen=500),
            'gyr_y': deque(maxlen=500),
            'gyr_z': deque(maxlen=500),
            'acc_mag': deque(maxlen=500),
            'gyr_mag': deque(maxlen=500),
            'fall_states': deque(maxlen=500),
            'motion_states': deque(maxlen=500)
        }

        # 统计数据
        self.stats = {
            'total_samples': 0,
            'fall_events': [],
            'motion_distribution': {0: 0, 1: 0, 2: 0, 3: 0},
            'fall_state_distribution': {0: 0, 1: 0, 2: 0, 3: 0},
            'max_acc': 0,
            'max_gyro': 0,
            'avg_sample_rate': 0,
            'last_sample_time': None
        }

        # 摔倒状态映射
        self.fall_states = {
            0: "正常",
            1: "冲击检测",
            2: "确认中",
            3: "摔倒确认"
        }

        # 运动状态映射
        self.motion_states = {
            0: "静止",
            1: "轻微运动",
            2: "中等运动",
            3: "剧烈运动"
        }

        # 状态颜色映射
        self.fall_colors = {
            0: "#28a745",  # 绿色 - 正常
            1: "#ffc107",  # 黄色 - 冲击
            2: "#fd7e14",  # 橙色 - 确认中
            3: "#dc3545"   # 红色 - 摔倒
        }

        self.motion_colors = {
            0: "#28a745",  # 绿色 - 静止
            1: "#17a2b8",  # 青色 - 轻微
            2: "#ffc107",  # 黄色 - 中等
            3: "#dc3545"   # 红色 - 剧烈
        }

        # GUI组件
        self.root = None
        self.status_labels = {}
        self.log_text = None
        self.chart_canvas = None
        self.chart_figure = None
        self.chart_axes = None

        # MQTT客户端
        self.mqtt_client = None
        self.connected = False
        self.message_queue = queue.Queue()

        # 测试控制
        self.testing = False
        self.auto_save_enabled = True
        self.chart_update_enabled = True
        self.sound_enabled = True

        # 线程控制
        self.update_thread = None
        self.chart_thread = None
        self.running = True

        self.setup_gui()
        self.setup_mqtt()
        self.start_update_threads()
    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)

            # MQTT配置
            mqtt_config = config.get('mqtt', {})
            self.mqtt_broker = mqtt_config.get('broker', 'localhost')
            self.mqtt_port = mqtt_config.get('port', 1883)
            self.mqtt_topic = mqtt_config.get('topic', 'doll/imu_status')
            self.mqtt_username = mqtt_config.get('username', '')
            self.mqtt_password = mqtt_config.get('password', '')

            # 测试配置
            test_config = config.get('test', {})
            self.log_max_entries = test_config.get('log_max_entries', 1000)
            self.auto_save_interval = test_config.get('auto_save_interval', 300)
            self.sound_enabled = test_config.get('alert_sound_enabled', True)

            # 显示配置
            display_config = config.get('display', {})
            self.window_width = display_config.get('window_width', 1200)
            self.window_height = display_config.get('window_height', 800)
            self.decimal_places = display_config.get('decimal_places', 3)

        except FileNotFoundError:
            self.log_message("配置文件不存在，使用默认配置", "orange")
            self.set_default_config()
        except Exception as e:
            self.log_message(f"配置文件加载失败: {e}，使用默认配置", "red")
            self.set_default_config()

    def set_default_config(self):
        """设置默认配置"""
        self.mqtt_broker = "localhost"
        self.mqtt_port = 1883
        self.mqtt_topic = "doll/imu_status"
        self.mqtt_username = ""
        self.mqtt_password = ""
        self.log_max_entries = 1000
        self.auto_save_interval = 300
        self.sound_enabled = True
        self.window_width = 1200
        self.window_height = 800
        self.decimal_places = 3

    def setup_gui(self):
        """设置详细的图形界面"""
        self.root = tk.Tk()
        self.root.title("ESP32S3 摔倒检测详细测试工具 v2.0")
        self.root.geometry(f"{self.window_width}x{self.window_height}")
        self.root.configure(bg='#f8f9fa')

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

        # 创建主要的Notebook（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标签页1: 实时监控
        self.setup_monitor_tab(notebook)

        # 标签页2: 数据图表
        self.setup_chart_tab(notebook)

        # 标签页3: 统计分析
        self.setup_stats_tab(notebook)

        # 标签页4: 测试控制
        self.setup_control_tab(notebook)

        # 状态栏
        self.setup_status_bar()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_monitor_tab(self, notebook):
        """设置实时监控标签页"""
        monitor_frame = ttk.Frame(notebook)
        notebook.add(monitor_frame, text="实时监控")

        # 左侧面板 - 连接和数据显示
        left_panel = ttk.Frame(monitor_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 连接状态
        conn_frame = ttk.LabelFrame(left_panel, text="连接状态", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(conn_frame, text="MQTT服务器:").grid(row=0, column=0, sticky=tk.W)
        self.status_labels['broker'] = ttk.Label(conn_frame, text=f"{self.mqtt_broker}:{self.mqtt_port}")
        self.status_labels['broker'].grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(conn_frame, text="连接状态:").grid(row=1, column=0, sticky=tk.W)
        self.status_labels['connection'] = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_labels['connection'].grid(row=1, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(conn_frame, text="数据速率:").grid(row=2, column=0, sticky=tk.W)
        self.status_labels['data_rate'] = ttk.Label(conn_frame, text="0 Hz")
        self.status_labels['data_rate'].grid(row=2, column=1, sticky=tk.W, padx=(10, 0))

        # IMU原始数据
        imu_frame = ttk.LabelFrame(left_panel, text="IMU原始数据", padding="10")
        imu_frame.pack(fill=tk.X, pady=(0, 10))

        # 加速度数据
        acc_subframe = ttk.LabelFrame(imu_frame, text="加速度 (g)", padding="5")
        acc_subframe.pack(fill=tk.X, pady=(0, 5))

        for i, axis in enumerate(['X', 'Y', 'Z']):
            ttk.Label(acc_subframe, text=f"{axis}:").grid(row=0, column=i*2, sticky=tk.W, padx=(0, 5))
            self.status_labels[f'a{axis.lower()}'] = ttk.Label(acc_subframe, text="0.000", font=('Courier', 10))
            self.status_labels[f'a{axis.lower()}'].grid(row=0, column=i*2+1, sticky=tk.W, padx=(0, 15))

        # 角速度数据
        gyr_subframe = ttk.LabelFrame(imu_frame, text="角速度 (°/s)", padding="5")
        gyr_subframe.pack(fill=tk.X, pady=(0, 5))

        for i, axis in enumerate(['X', 'Y', 'Z']):
            ttk.Label(gyr_subframe, text=f"{axis}:").grid(row=0, column=i*2, sticky=tk.W, padx=(0, 5))
            self.status_labels[f'g{axis.lower()}'] = ttk.Label(gyr_subframe, text="0.000", font=('Courier', 10))
            self.status_labels[f'g{axis.lower()}'].grid(row=0, column=i*2+1, sticky=tk.W, padx=(0, 15))

        # 角度数据
        angle_subframe = ttk.LabelFrame(imu_frame, text="姿态角 (°)", padding="5")
        angle_subframe.pack(fill=tk.X)

        for i, axis in enumerate(['X', 'Y', 'Z']):
            ttk.Label(angle_subframe, text=f"{axis}:").grid(row=0, column=i*2, sticky=tk.W, padx=(0, 5))
            self.status_labels[f'angle_{axis.lower()}'] = ttk.Label(angle_subframe, text="0.000", font=('Courier', 10))
            self.status_labels[f'angle_{axis.lower()}'].grid(row=0, column=i*2+1, sticky=tk.W, padx=(0, 15))

        # 右侧面板 - 状态和统计
        right_panel = ttk.Frame(monitor_frame)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 检测状态
        status_frame = ttk.LabelFrame(right_panel, text="检测状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(status_frame, text="运动状态:").grid(row=0, column=0, sticky=tk.W)
        self.status_labels['motion'] = ttk.Label(status_frame, text="静止", font=('Arial', 12, 'bold'))
        self.status_labels['motion'].grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(status_frame, text="摔倒状态:").grid(row=1, column=0, sticky=tk.W)
        self.status_labels['fall'] = ttk.Label(status_frame, text="正常", font=('Arial', 12, 'bold'))
        self.status_labels['fall'].grid(row=1, column=1, sticky=tk.W, padx=(10, 0))

        # 计算数据
        calc_frame = ttk.LabelFrame(right_panel, text="计算数据", padding="10")
        calc_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(calc_frame, text="加速度模长:").grid(row=0, column=0, sticky=tk.W)
        self.status_labels['acc_mag'] = ttk.Label(calc_frame, text="0.000 g", font=('Courier', 10))
        self.status_labels['acc_mag'].grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(calc_frame, text="角速度模长:").grid(row=1, column=0, sticky=tk.W)
        self.status_labels['gyr_mag'] = ttk.Label(calc_frame, text="0.000 °/s", font=('Courier', 10))
        self.status_labels['gyr_mag'].grid(row=1, column=1, sticky=tk.W, padx=(10, 0))

        # 统计信息
        stats_frame = ttk.LabelFrame(right_panel, text="测试统计", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(stats_frame, text="测试时长:").grid(row=0, column=0, sticky=tk.W)
        self.status_labels['test_duration'] = ttk.Label(stats_frame, text="00:00:00")
        self.status_labels['test_duration'].grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(stats_frame, text="摔倒次数:").grid(row=1, column=0, sticky=tk.W)
        self.status_labels['fall_count'] = ttk.Label(stats_frame, text="0", foreground="red", font=('Arial', 12, 'bold'))
        self.status_labels['fall_count'].grid(row=1, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(stats_frame, text="误报次数:").grid(row=2, column=0, sticky=tk.W)
        self.status_labels['false_positive'] = ttk.Label(stats_frame, text="0", foreground="orange")
        self.status_labels['false_positive'].grid(row=2, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(stats_frame, text="数据样本:").grid(row=3, column=0, sticky=tk.W)
        self.status_labels['sample_count'] = ttk.Label(stats_frame, text="0")
        self.status_labels['sample_count'].grid(row=3, column=1, sticky=tk.W, padx=(10, 0))

        # 日志显示
        log_frame = ttk.LabelFrame(right_panel, text="实时日志", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def setup_chart_tab(self, notebook):
        """设置数据图表标签页"""
        chart_frame = ttk.Frame(notebook)
        notebook.add(chart_frame, text="数据图表")

        # 图表控制面板
        control_panel = ttk.Frame(chart_frame)
        control_panel.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(control_panel, text="图表显示:").pack(side=tk.LEFT)

        self.chart_acc_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="加速度", variable=self.chart_acc_var,
                       command=self.update_chart_display).pack(side=tk.LEFT, padx=5)

        self.chart_gyr_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="角速度", variable=self.chart_gyr_var,
                       command=self.update_chart_display).pack(side=tk.LEFT, padx=5)

        self.chart_mag_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="模长", variable=self.chart_mag_var,
                       command=self.update_chart_display).pack(side=tk.LEFT, padx=5)

        self.chart_fall_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="摔倒状态", variable=self.chart_fall_var,
                       command=self.update_chart_display).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_panel, text="保存图表", command=self.save_chart).pack(side=tk.RIGHT, padx=5)
        ttk.Button(control_panel, text="清除图表", command=self.clear_chart).pack(side=tk.RIGHT, padx=5)

        # 创建matplotlib图表
        self.setup_matplotlib_chart(chart_frame)

    def setup_matplotlib_chart(self, parent):
        """设置matplotlib图表"""
        try:
            self.chart_figure, self.chart_axes = plt.subplots(4, 1, figsize=(12, 8))
            self.chart_figure.suptitle('ESP32S3 IMU实时数据', fontsize=14)

            # 设置子图
            self.chart_axes[0].set_title('加速度 (g)')
            self.chart_axes[0].set_ylabel('加速度')
            self.chart_axes[0].grid(True, alpha=0.3)
            self.chart_axes[0].legend(['X', 'Y', 'Z'], loc='upper right')

            self.chart_axes[1].set_title('角速度 (°/s)')
            self.chart_axes[1].set_ylabel('角速度')
            self.chart_axes[1].grid(True, alpha=0.3)
            self.chart_axes[1].legend(['X', 'Y', 'Z'], loc='upper right')

            self.chart_axes[2].set_title('模长')
            self.chart_axes[2].set_ylabel('幅值')
            self.chart_axes[2].grid(True, alpha=0.3)
            self.chart_axes[2].legend(['加速度模长', '角速度模长'], loc='upper right')

            self.chart_axes[3].set_title('摔倒检测状态')
            self.chart_axes[3].set_ylabel('状态')
            self.chart_axes[3].set_xlabel('时间 (样本)')
            self.chart_axes[3].grid(True, alpha=0.3)
            self.chart_axes[3].set_ylim(-0.5, 3.5)
            self.chart_axes[3].set_yticks([0, 1, 2, 3])
            self.chart_axes[3].set_yticklabels(['正常', '冲击', '确认中', '摔倒'])

            plt.tight_layout()

            # 嵌入到tkinter
            self.chart_canvas = FigureCanvasTkAgg(self.chart_figure, parent)
            self.chart_canvas.draw()
            self.chart_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        except ImportError:
            # 如果matplotlib不可用，显示提示
            error_label = ttk.Label(parent, text="matplotlib未安装，无法显示图表\n请运行: pip install matplotlib",
                                  font=('Arial', 12), foreground='red')
            error_label.pack(expand=True)

    def setup_stats_tab(self, notebook):
        """设置统计分析标签页"""
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="统计分析")

        # 左侧 - 基础统计
        left_stats = ttk.LabelFrame(stats_frame, text="基础统计", padding="10")
        left_stats.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 5), pady=10)

        # 测试概览
        overview_frame = ttk.LabelFrame(left_stats, text="测试概览", padding="5")
        overview_frame.pack(fill=tk.X, pady=(0, 10))

        self.stats_labels = {}
        stats_items = [
            ("开始时间:", "start_time"),
            ("测试时长:", "duration"),
            ("总样本数:", "total_samples"),
            ("平均采样率:", "avg_rate"),
            ("最大加速度:", "max_acc"),
            ("最大角速度:", "max_gyr")
        ]

        for i, (label, key) in enumerate(stats_items):
            ttk.Label(overview_frame, text=label).grid(row=i, column=0, sticky=tk.W, pady=2)
            self.stats_labels[key] = ttk.Label(overview_frame, text="--")
            self.stats_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 摔倒事件统计
        fall_stats_frame = ttk.LabelFrame(left_stats, text="摔倒事件", padding="5")
        fall_stats_frame.pack(fill=tk.X, pady=(0, 10))

        fall_items = [
            ("摔倒次数:", "fall_count"),
            ("误报次数:", "false_positive"),
            ("检测准确率:", "accuracy"),
            ("平均间隔:", "avg_interval"),
            ("最后摔倒:", "last_fall")
        ]

        for i, (label, key) in enumerate(fall_items):
            ttk.Label(fall_stats_frame, text=label).grid(row=i, column=0, sticky=tk.W, pady=2)
            self.stats_labels[key] = ttk.Label(fall_stats_frame, text="--")
            self.stats_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 右侧 - 分布统计
        right_stats = ttk.LabelFrame(stats_frame, text="分布统计", padding="10")
        right_stats.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 10), pady=10)

        # 运动状态分布
        motion_dist_frame = ttk.LabelFrame(right_stats, text="运动状态分布", padding="5")
        motion_dist_frame.pack(fill=tk.X, pady=(0, 10))

        self.motion_dist_labels = {}
        for i, (state, name) in enumerate(self.motion_states.items()):
            ttk.Label(motion_dist_frame, text=f"{name}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.motion_dist_labels[state] = ttk.Label(motion_dist_frame, text="0 (0%)")
            self.motion_dist_labels[state].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 摔倒状态分布
        fall_dist_frame = ttk.LabelFrame(right_stats, text="摔倒状态分布", padding="5")
        fall_dist_frame.pack(fill=tk.X, pady=(0, 10))

        self.fall_dist_labels = {}
        for i, (state, name) in enumerate(self.fall_states.items()):
            ttk.Label(fall_dist_frame, text=f"{name}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.fall_dist_labels[state] = ttk.Label(fall_dist_frame, text="0 (0%)")
            self.fall_dist_labels[state].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 摔倒事件列表
        events_frame = ttk.LabelFrame(right_stats, text="摔倒事件列表", padding="5")
        events_frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview显示摔倒事件
        columns = ('时间', '加速度模长', '角速度模长', '姿态角X', '姿态角Y')
        self.events_tree = ttk.Treeview(events_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.events_tree.heading(col, text=col)
            self.events_tree.column(col, width=80)

        scrollbar_events = ttk.Scrollbar(events_frame, orient=tk.VERTICAL, command=self.events_tree.yview)
        self.events_tree.configure(yscrollcommand=scrollbar_events.set)

        self.events_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_events.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_control_tab(self, notebook):
        """设置测试控制标签页"""
        control_frame = ttk.Frame(notebook)
        notebook.add(control_frame, text="测试控制")

        # 连接控制
        conn_control_frame = ttk.LabelFrame(control_frame, text="连接控制", padding="10")
        conn_control_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        ttk.Label(conn_control_frame, text="MQTT服务器:").grid(row=0, column=0, sticky=tk.W)
        self.broker_entry = ttk.Entry(conn_control_frame, width=20)
        self.broker_entry.insert(0, self.mqtt_broker)
        self.broker_entry.grid(row=0, column=1, padx=5)

        ttk.Label(conn_control_frame, text="端口:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.port_entry = ttk.Entry(conn_control_frame, width=8)
        self.port_entry.insert(0, str(self.mqtt_port))
        self.port_entry.grid(row=0, column=3, padx=5)

        ttk.Label(conn_control_frame, text="主题:").grid(row=1, column=0, sticky=tk.W)
        self.topic_entry = ttk.Entry(conn_control_frame, width=30)
        self.topic_entry.insert(0, self.mqtt_topic)
        self.topic_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5)

        ttk.Button(conn_control_frame, text="更新配置", command=self.update_mqtt_config).grid(row=1, column=3, padx=5)

        # 测试控制
        test_control_frame = ttk.LabelFrame(control_frame, text="测试控制", padding="10")
        test_control_frame.pack(fill=tk.X, padx=10, pady=5)

        button_frame1 = ttk.Frame(test_control_frame)
        button_frame1.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame1, text="开始测试", command=self.start_test,
                  style='Success.TButton').pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame1, text="停止测试", command=self.stop_test,
                  style='Danger.TButton').pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame1, text="暂停/恢复", command=self.toggle_pause).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame1, text="重置统计", command=self.reset_stats).pack(side=tk.LEFT, padx=5)

        button_frame2 = ttk.Frame(test_control_frame)
        button_frame2.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame2, text="标记误报", command=self.mark_false_positive).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame2, text="手动触发", command=self.manual_trigger).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame2, text="测试声音", command=self.test_sound).pack(side=tk.LEFT, padx=5)

        # 设置选项
        settings_frame = ttk.LabelFrame(control_frame, text="设置选项", padding="10")
        settings_frame.pack(fill=tk.X, padx=10, pady=5)

        self.auto_save_var = tk.BooleanVar(value=self.auto_save_enabled)
        ttk.Checkbutton(settings_frame, text="自动保存数据", variable=self.auto_save_var).pack(anchor=tk.W)

        self.sound_var = tk.BooleanVar(value=self.sound_enabled)
        ttk.Checkbutton(settings_frame, text="声音提示", variable=self.sound_var).pack(anchor=tk.W)

        self.chart_update_var = tk.BooleanVar(value=self.chart_update_enabled)
        ttk.Checkbutton(settings_frame, text="实时图表更新", variable=self.chart_update_var).pack(anchor=tk.W)

        # 数据管理
        data_frame = ttk.LabelFrame(control_frame, text="数据管理", padding="10")
        data_frame.pack(fill=tk.X, padx=10, pady=5)

        button_frame3 = ttk.Frame(data_frame)
        button_frame3.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame3, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame3, text="导出CSV", command=self.export_csv).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame3, text="加载数据", command=self.load_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame3, text="清除数据", command=self.clear_all_data).pack(side=tk.LEFT, padx=5)

        # 高级设置
        advanced_frame = ttk.LabelFrame(control_frame, text="高级设置", padding="10")
        advanced_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        ttk.Label(advanced_frame, text="数据缓冲区大小:").grid(row=0, column=0, sticky=tk.W)
        self.buffer_size_var = tk.StringVar(value="500")
        buffer_spinbox = ttk.Spinbox(advanced_frame, from_=100, to=2000, textvariable=self.buffer_size_var, width=10)
        buffer_spinbox.grid(row=0, column=1, padx=5, sticky=tk.W)

        ttk.Label(advanced_frame, text="图表更新间隔(ms):").grid(row=1, column=0, sticky=tk.W)
        self.chart_interval_var = tk.StringVar(value="100")
        interval_spinbox = ttk.Spinbox(advanced_frame, from_=50, to=1000, textvariable=self.chart_interval_var, width=10)
        interval_spinbox.grid(row=1, column=1, padx=5, sticky=tk.W)

        ttk.Button(advanced_frame, text="应用设置", command=self.apply_advanced_settings).grid(row=2, column=0, columnspan=2, pady=10)

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        self.status_text = ttk.Label(self.status_bar, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)

        self.time_label = ttk.Label(self.status_bar, text="", relief=tk.SUNKEN)
        self.time_label.pack(side=tk.RIGHT, padx=2, pady=2)

        # 更新时间显示
        self.update_time_display()

    def update_time_display(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time_display)

    def setup_mqtt(self):
        """设置MQTT连接"""
        self.mqtt_client = mqtt.Client()
        self.mqtt_client.on_connect = self.on_mqtt_connect
        self.mqtt_client.on_message = self.on_mqtt_message
        self.mqtt_client.on_disconnect = self.on_mqtt_disconnect

        if self.mqtt_username and self.mqtt_password:
            self.mqtt_client.username_pw_set(self.mqtt_username, self.mqtt_password)

    def start_update_threads(self):
        """启动更新线程"""
        # 数据处理线程
        self.update_thread = threading.Thread(target=self.data_update_loop, daemon=True)
        self.update_thread.start()

        # 图表更新线程
        if hasattr(self, 'chart_canvas'):
            self.chart_thread = threading.Thread(target=self.chart_update_loop, daemon=True)
            self.chart_thread.start()

    def data_update_loop(self):
        """数据更新循环"""
        print("🔄 数据更新循环启动")
        while self.running:
            try:
                # 处理MQTT消息队列
                queue_size = self.message_queue.qsize()
                if queue_size > 0:
                    print(f"📥 队列中有 {queue_size} 条消息待处理")

                while not self.message_queue.empty():
                    data = self.message_queue.get_nowait()
                    print(f"🔄 处理IMU数据: {data.get('device_id', 'unknown')}")
                    self.process_imu_data(data)

                # 更新GUI显示
                if self.testing:
                    self.root.after_idle(self.update_gui_stats)

                time.sleep(0.05)  # 20Hz更新频率
            except Exception as e:
                self.log_message(f"数据更新错误: {e}", "red")

    def chart_update_loop(self):
        """图表更新循环"""
        while self.running:
            try:
                if self.testing and self.chart_update_var.get() and hasattr(self, 'chart_canvas'):
                    self.root.after_idle(self.update_chart)

                interval = int(self.chart_interval_var.get())
                time.sleep(interval / 1000.0)
            except Exception as e:
                self.log_message(f"图表更新错误: {e}", "red")

    def on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            self.connected = True
            self.status_labels['connection'].config(text="已连接", foreground="green")
            self.status_text.config(text=f"MQTT连接成功 - {self.mqtt_broker}:{self.mqtt_port}")
            client.subscribe(self.mqtt_topic)
            self.log_message(f"✅ MQTT连接成功，监听主题: {self.mqtt_topic}", "green")
        else:
            self.connected = False
            error_msg = f"MQTT连接失败，错误码: {rc}"
            self.status_labels['connection'].config(text="连接失败", foreground="red")
            self.status_text.config(text=error_msg)
            self.log_message(f"❌ {error_msg}", "red")

    def on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        self.connected = False
        self.status_labels['connection'].config(text="连接断开", foreground="red")
        self.status_text.config(text="MQTT连接断开")
        self.log_message("⚠️ MQTT连接断开", "orange")

    def on_mqtt_message(self, client, userdata, msg):
        """接收MQTT消息"""
        try:
            payload_str = msg.payload.decode()
            print(f"🔍 测试工具收到MQTT消息: {len(payload_str)} chars")
            print(f"   主题: {msg.topic}")
            print(f"   内容: {payload_str[:100]}{'...' if len(payload_str) > 100 else ''}")

            data = json.loads(payload_str)
            self.message_queue.put(data)
            print(f"✅ 数据已放入队列")
        except Exception as e:
            print(f"❌ 数据解析错误: {e}")
            print(f"   原始数据: {msg.payload}")
            self.log_message(f"❌ 数据解析错误: {e}", "red")

    def process_imu_data(self, data):
        """处理IMU数据"""
        if not self.testing:
            return

        timestamp = datetime.now()
        self.imu_data = data

        # 提取数据
        ax = data.get('ax', 0)
        ay = data.get('ay', 0)
        az = data.get('az', 0)
        gx = data.get('gx', 0)
        gy = data.get('gy', 0)
        gz = data.get('gz', 0)
        angle_x = data.get('angle_x', 0)
        angle_y = data.get('angle_y', 0)
        angle_z = data.get('angle_z', 0)
        motion_state = data.get('imu_type', 0)
        fall_state = data.get('fall_state', 0)

        # 计算模长
        acc_mag = (ax**2 + ay**2 + az**2)**0.5
        gyr_mag = (gx**2 + gy**2 + gz**2)**0.5

        # 更新实时数据缓冲区
        self.real_time_data['timestamps'].append(timestamp)
        self.real_time_data['acc_x'].append(ax)
        self.real_time_data['acc_y'].append(ay)
        self.real_time_data['acc_z'].append(az)
        self.real_time_data['gyr_x'].append(gx)
        self.real_time_data['gyr_y'].append(gy)
        self.real_time_data['gyr_z'].append(gz)
        self.real_time_data['acc_mag'].append(acc_mag)
        self.real_time_data['gyr_mag'].append(gyr_mag)
        self.real_time_data['fall_states'].append(fall_state)
        self.real_time_data['motion_states'].append(motion_state)

        # 更新统计数据
        self.stats['total_samples'] += 1
        self.stats['motion_distribution'][motion_state] += 1
        self.stats['fall_state_distribution'][fall_state] += 1
        self.stats['max_acc'] = max(self.stats['max_acc'], acc_mag)
        self.stats['max_gyro'] = max(self.stats['max_gyro'], gyr_mag)

        # 计算采样率
        if self.stats['last_sample_time']:
            time_diff = (timestamp - self.stats['last_sample_time']).total_seconds()
            if time_diff > 0:
                current_rate = 1.0 / time_diff
                if self.stats['avg_sample_rate'] == 0:
                    self.stats['avg_sample_rate'] = current_rate
                else:
                    self.stats['avg_sample_rate'] = 0.9 * self.stats['avg_sample_rate'] + 0.1 * current_rate
        self.stats['last_sample_time'] = timestamp

        # 检查摔倒状态
        if fall_state == 3:  # 摔倒确认
            self.handle_fall_detection(timestamp, data, acc_mag, gyr_mag)

        # 记录数据
        log_entry = {
            'timestamp': timestamp.isoformat(),
            'data': data.copy(),
            'calculated': {
                'acc_mag': acc_mag,
                'gyr_mag': gyr_mag
            }
        }
        self.data_log.append(log_entry)

        # 限制日志长度
        if len(self.data_log) > self.log_max_entries:
            self.data_log = self.data_log[-int(self.log_max_entries * 0.8):]

    def handle_fall_detection(self, timestamp, data, acc_mag, gyr_mag):
        """处理摔倒检测事件"""
        self.fall_count += 1

        # 记录摔倒事件
        fall_event = {
            'timestamp': timestamp,
            'data': data.copy(),
            'acc_mag': acc_mag,
            'gyr_mag': gyr_mag,
            'angle_x': data.get('angle_x', 0),
            'angle_y': data.get('angle_y', 0),
            'angle_z': data.get('angle_z', 0)
        }
        self.stats['fall_events'].append(fall_event)

        # 更新GUI
        self.status_labels['fall_count'].config(text=str(self.fall_count))

        # 日志记录
        time_str = timestamp.strftime("%H:%M:%S.%f")[:-3]
        self.log_message(f"🚨🚨🚨 摔倒检测！第{self.fall_count}次 - {time_str}", "red")
        self.log_message(f"    加速度模长: {acc_mag:.3f}g, 角速度模长: {gyr_mag:.3f}°/s", "red")
        self.log_message(f"    姿态角: X={data.get('angle_x', 0):.1f}° Y={data.get('angle_y', 0):.1f}° Z={data.get('angle_z', 0):.1f}°", "red")

        # 添加到事件列表
        self.events_tree.insert('', 0, values=(
            time_str,
            f"{acc_mag:.3f}g",
            f"{gyr_mag:.3f}°/s",
            f"{data.get('angle_x', 0):.1f}°",
            f"{data.get('angle_y', 0):.1f}°"
        ))

        # 声音提示
        if self.sound_var.get():
            self.play_alert_sound()

        # 状态栏提示
        self.status_text.config(text=f"🚨 摔倒检测！第{self.fall_count}次 - {time_str}")

        # 自动保存
        if self.auto_save_var.get():
            self.auto_save_data()

    def update_gui_data(self):
        """更新GUI数据显示"""
        if not self.imu_data:
            return

        data = self.imu_data

        # 更新原始数据显示
        for axis in ['x', 'y', 'z']:
            if f'a{axis}' in data:
                self.status_labels[f'a{axis}'].config(text=f"{data[f'a{axis}']:.{self.decimal_places}f}")
            if f'g{axis}' in data:
                self.status_labels[f'g{axis}'].config(text=f"{data[f'g{axis}']:.{self.decimal_places}f}")
            if f'angle_{axis}' in data:
                self.status_labels[f'angle_{axis}'].config(text=f"{data[f'angle_{axis}']:.{self.decimal_places}f}")

        # 计算和显示模长
        ax, ay, az = data.get('ax', 0), data.get('ay', 0), data.get('az', 0)
        gx, gy, gz = data.get('gx', 0), data.get('gy', 0), data.get('gz', 0)
        acc_mag = (ax**2 + ay**2 + az**2)**0.5
        gyr_mag = (gx**2 + gy**2 + gz**2)**0.5

        self.status_labels['acc_mag'].config(text=f"{acc_mag:.{self.decimal_places}f} g")
        self.status_labels['gyr_mag'].config(text=f"{gyr_mag:.{self.decimal_places}f} °/s")

        # 更新状态显示
        motion_state = data.get('imu_type', 0)
        fall_state = data.get('fall_state', 0)

        motion_text = self.motion_states.get(motion_state, "未知")
        motion_color = self.motion_colors.get(motion_state, "black")
        self.status_labels['motion'].config(text=motion_text, foreground=motion_color)

        fall_text = self.fall_states.get(fall_state, "未知")
        fall_color = self.fall_colors.get(fall_state, "black")
        self.status_labels['fall'].config(text=fall_text, foreground=fall_color)

        # 更新数据速率
        if self.stats['avg_sample_rate'] > 0:
            self.status_labels['data_rate'].config(text=f"{self.stats['avg_sample_rate']:.1f} Hz")

    def update_gui_stats(self):
        """更新GUI统计信息"""
        # 测试时长
        if self.testing:
            duration = datetime.now() - self.test_start_time
            duration_str = str(duration).split('.')[0]  # 移除微秒
            self.status_labels['test_duration'].config(text=duration_str)

        # 样本计数
        self.status_labels['sample_count'].config(text=str(self.stats['total_samples']))

        # 误报计数
        self.status_labels['false_positive'].config(text=str(self.false_positive_count))

        # 更新统计标签页
        self.update_stats_tab()

        # 更新数据显示
        self.update_gui_data()

    def update_stats_tab(self):
        """更新统计标签页"""
        if not hasattr(self, 'stats_labels'):
            return

        # 基础统计
        self.stats_labels['start_time'].config(text=self.test_start_time.strftime("%H:%M:%S"))

        if self.testing:
            duration = datetime.now() - self.test_start_time
            self.stats_labels['duration'].config(text=str(duration).split('.')[0])
        else:
            self.stats_labels['duration'].config(text="--")

        self.stats_labels['total_samples'].config(text=str(self.stats['total_samples']))
        self.stats_labels['avg_rate'].config(text=f"{self.stats['avg_sample_rate']:.1f} Hz")
        self.stats_labels['max_acc'].config(text=f"{self.stats['max_acc']:.3f} g")
        self.stats_labels['max_gyr'].config(text=f"{self.stats['max_gyro']:.3f} °/s")

        # 摔倒统计
        self.stats_labels['fall_count'].config(text=str(self.fall_count))
        self.stats_labels['false_positive'].config(text=str(self.false_positive_count))

        if self.fall_count > 0 and self.false_positive_count >= 0:
            accuracy = (self.fall_count / (self.fall_count + self.false_positive_count)) * 100
            self.stats_labels['accuracy'].config(text=f"{accuracy:.1f}%")
        else:
            self.stats_labels['accuracy'].config(text="--")

        if len(self.stats['fall_events']) > 1:
            intervals = []
            for i in range(1, len(self.stats['fall_events'])):
                interval = (self.stats['fall_events'][i]['timestamp'] -
                          self.stats['fall_events'][i-1]['timestamp']).total_seconds()
                intervals.append(interval)
            avg_interval = statistics.mean(intervals)
            self.stats_labels['avg_interval'].config(text=f"{avg_interval:.1f}s")
        else:
            self.stats_labels['avg_interval'].config(text="--")

        if self.stats['fall_events']:
            last_fall = self.stats['fall_events'][-1]['timestamp']
            self.stats_labels['last_fall'].config(text=last_fall.strftime("%H:%M:%S"))
        else:
            self.stats_labels['last_fall'].config(text="--")

        # 分布统计
        total_samples = max(self.stats['total_samples'], 1)

        for state in self.motion_states.keys():
            count = self.stats['motion_distribution'][state]
            percentage = (count / total_samples) * 100
            self.motion_dist_labels[state].config(text=f"{count} ({percentage:.1f}%)")

        for state in self.fall_states.keys():
            count = self.stats['fall_state_distribution'][state]
            percentage = (count / total_samples) * 100
            self.fall_dist_labels[state].config(text=f"{count} ({percentage:.1f}%)")

    def update_chart(self):
        """更新实时图表"""
        if not hasattr(self, 'chart_axes') or not self.real_time_data['timestamps']:
            return

        try:
            # 清除旧数据
            for ax in self.chart_axes:
                ax.clear()

            # 获取数据
            timestamps = list(self.real_time_data['timestamps'])
            if len(timestamps) < 2:
                return

            # 转换时间戳为相对秒数
            start_time = timestamps[0]
            time_seconds = [(t - start_time).total_seconds() for t in timestamps]

            # 加速度图
            if self.chart_acc_var.get():
                self.chart_axes[0].plot(time_seconds, list(self.real_time_data['acc_x']), 'r-', label='X', alpha=0.7)
                self.chart_axes[0].plot(time_seconds, list(self.real_time_data['acc_y']), 'g-', label='Y', alpha=0.7)
                self.chart_axes[0].plot(time_seconds, list(self.real_time_data['acc_z']), 'b-', label='Z', alpha=0.7)
                self.chart_axes[0].set_title('加速度 (g)')
                self.chart_axes[0].set_ylabel('加速度')
                self.chart_axes[0].legend()
                self.chart_axes[0].grid(True, alpha=0.3)

            # 角速度图
            if self.chart_gyr_var.get():
                self.chart_axes[1].plot(time_seconds, list(self.real_time_data['gyr_x']), 'r-', label='X', alpha=0.7)
                self.chart_axes[1].plot(time_seconds, list(self.real_time_data['gyr_y']), 'g-', label='Y', alpha=0.7)
                self.chart_axes[1].plot(time_seconds, list(self.real_time_data['gyr_z']), 'b-', label='Z', alpha=0.7)
                self.chart_axes[1].set_title('角速度 (°/s)')
                self.chart_axes[1].set_ylabel('角速度')
                self.chart_axes[1].legend()
                self.chart_axes[1].grid(True, alpha=0.3)

            # 模长图
            if self.chart_mag_var.get():
                self.chart_axes[2].plot(time_seconds, list(self.real_time_data['acc_mag']), 'purple', label='加速度模长', alpha=0.8)
                self.chart_axes[2].plot(time_seconds, list(self.real_time_data['gyr_mag']), 'orange', label='角速度模长', alpha=0.8)
                self.chart_axes[2].set_title('模长')
                self.chart_axes[2].set_ylabel('幅值')
                self.chart_axes[2].legend()
                self.chart_axes[2].grid(True, alpha=0.3)

            # 摔倒状态图
            if self.chart_fall_var.get():
                fall_states = list(self.real_time_data['fall_states'])
                self.chart_axes[3].plot(time_seconds, fall_states, 'red', linewidth=2, label='摔倒状态')
                self.chart_axes[3].set_title('摔倒检测状态')
                self.chart_axes[3].set_ylabel('状态')
                self.chart_axes[3].set_xlabel('时间 (秒)')
                self.chart_axes[3].set_ylim(-0.5, 3.5)
                self.chart_axes[3].set_yticks([0, 1, 2, 3])
                self.chart_axes[3].set_yticklabels(['正常', '冲击', '确认中', '摔倒'])
                self.chart_axes[3].grid(True, alpha=0.3)

                # 标记摔倒事件
                fall_indices = [i for i, state in enumerate(fall_states) if state == 3]
                for idx in fall_indices:
                    if idx < len(time_seconds):
                        for ax in self.chart_axes:
                            ax.axvline(x=time_seconds[idx], color='red', linestyle='--', alpha=0.5)

            plt.tight_layout()
            self.chart_canvas.draw()

        except Exception as e:
            self.log_message(f"图表更新错误: {e}", "red")

    # 控制功能
    def start_test(self):
        """开始测试"""
        try:
            # 更新MQTT配置
            self.mqtt_broker = self.broker_entry.get()
            self.mqtt_port = int(self.port_entry.get())
            self.mqtt_topic = self.topic_entry.get()

            # 重新设置MQTT客户端
            if self.mqtt_client:
                self.mqtt_client.disconnect()
            self.setup_mqtt()

            # 连接MQTT
            self.mqtt_client.connect(self.mqtt_broker, self.mqtt_port, 60)
            self.mqtt_client.loop_start()

            # 重置统计
            self.test_start_time = datetime.now()
            self.testing = True

            self.status_text.config(text="测试开始...")
            self.log_message("🚀 测试开始", "green")

        except Exception as e:
            self.log_message(f"❌ 启动测试失败: {e}", "red")
            messagebox.showerror("错误", f"启动测试失败: {e}")

    def stop_test(self):
        """停止测试"""
        self.testing = False
        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()

        self.status_text.config(text="测试停止")
        self.log_message("⏹️ 测试停止", "orange")

    def toggle_pause(self):
        """暂停/恢复测试"""
        if hasattr(self, 'paused'):
            self.paused = not self.paused
        else:
            self.paused = True

        status = "暂停" if self.paused else "恢复"
        self.log_message(f"⏸️ 测试{status}", "blue")

    def reset_stats(self):
        """重置统计数据"""
        if messagebox.askyesno("确认", "确定要重置所有统计数据吗？"):
            self.fall_count = 0
            self.false_positive_count = 0
            self.stats = {
                'total_samples': 0,
                'fall_events': [],
                'motion_distribution': {0: 0, 1: 0, 2: 0, 3: 0},
                'fall_state_distribution': {0: 0, 1: 0, 2: 0, 3: 0},
                'max_acc': 0,
                'max_gyro': 0,
                'avg_sample_rate': 0,
                'last_sample_time': None
            }

            # 清除事件列表
            for item in self.events_tree.get_children():
                self.events_tree.delete(item)

            self.log_message("🔄 统计数据已重置", "blue")

    def mark_false_positive(self):
        """标记误报"""
        self.false_positive_count += 1
        self.status_labels['false_positive'].config(text=str(self.false_positive_count))
        self.log_message(f"⚠️ 标记误报，当前误报次数: {self.false_positive_count}", "orange")

    def manual_trigger(self):
        """手动触发摔倒检测（用于测试）"""
        fake_data = {
            'ax': 3.0, 'ay': 1.0, 'az': 0.5,
            'gx': 200.0, 'gy': 150.0, 'gz': 100.0,
            'angle_x': 45.0, 'angle_y': 30.0, 'angle_z': 15.0,
            'imu_type': 3, 'fall_state': 3
        }
        self.process_imu_data(fake_data)
        self.log_message("🧪 手动触发摔倒检测", "blue")

    def test_sound(self):
        """测试声音"""
        self.play_alert_sound()
        self.log_message("🔊 测试声音", "blue")

    def update_mqtt_config(self):
        """更新MQTT配置"""
        self.mqtt_broker = self.broker_entry.get()
        self.mqtt_port = int(self.port_entry.get())
        self.mqtt_topic = self.topic_entry.get()
        self.status_labels['broker'].config(text=f"{self.mqtt_broker}:{self.mqtt_port}")
        self.log_message("⚙️ MQTT配置已更新", "blue")

    def apply_advanced_settings(self):
        """应用高级设置"""
        try:
            buffer_size = int(self.buffer_size_var.get())
            # 更新缓冲区大小
            for key in self.real_time_data:
                self.real_time_data[key] = deque(list(self.real_time_data[key]), maxlen=buffer_size)

            self.log_message(f"⚙️ 高级设置已应用，缓冲区大小: {buffer_size}", "blue")
        except Exception as e:
            self.log_message(f"❌ 应用设置失败: {e}", "red")

    def update_chart_display(self):
        """更新图表显示选项"""
        self.log_message("📊 图表显示选项已更新", "blue")

    def clear_chart(self):
        """清除图表数据"""
        for key in self.real_time_data:
            self.real_time_data[key].clear()
        self.log_message("🗑️ 图表数据已清除", "blue")

    def save_chart(self):
        """保存图表"""
        if hasattr(self, 'chart_figure'):
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("All files", "*.*")]
            )
            if filename:
                self.chart_figure.savefig(filename, dpi=300, bbox_inches='tight')
                self.log_message(f"💾 图表已保存: {filename}", "green")

    # 数据管理功能
    def save_log(self):
        """保存测试日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            initialvalue=f"fall_test_log_{timestamp}.json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            test_summary = {
                'test_info': {
                    'start_time': self.test_start_time.isoformat(),
                    'end_time': datetime.now().isoformat(),
                    'mqtt_config': {
                        'broker': self.mqtt_broker,
                        'port': self.mqtt_port,
                        'topic': self.mqtt_topic
                    }
                },
                'statistics': {
                    'fall_count': self.fall_count,
                    'false_positive_count': self.false_positive_count,
                    'total_samples': self.stats['total_samples'],
                    'max_acc': self.stats['max_acc'],
                    'max_gyro': self.stats['max_gyro'],
                    'avg_sample_rate': self.stats['avg_sample_rate'],
                    'motion_distribution': self.stats['motion_distribution'],
                    'fall_state_distribution': self.stats['fall_state_distribution']
                },
                'fall_events': [
                    {
                        'timestamp': event['timestamp'].isoformat(),
                        'data': event['data'],
                        'acc_mag': event['acc_mag'],
                        'gyr_mag': event['gyr_mag'],
                        'angles': {
                            'x': event['angle_x'],
                            'y': event['angle_y'],
                            'z': event['angle_z']
                        }
                    } for event in self.stats['fall_events']
                ],
                'data_log': self.data_log[-1000:]  # 保存最近1000条数据
            }

            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(test_summary, f, indent=2, ensure_ascii=False)
                self.log_message(f"💾 日志已保存: {filename}", "green")
                messagebox.showinfo("成功", f"日志已保存到:\n{filename}")
            except Exception as e:
                self.log_message(f"❌ 保存失败: {e}", "red")
                messagebox.showerror("错误", f"保存失败: {e}")

    def export_csv(self):
        """导出CSV格式数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            initialvalue=f"fall_test_data_{timestamp}.csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # 写入表头
                    headers = ['timestamp', 'ax', 'ay', 'az', 'gx', 'gy', 'gz',
                              'angle_x', 'angle_y', 'angle_z', 'imu_type', 'fall_state',
                              'acc_mag', 'gyr_mag']
                    writer.writerow(headers)

                    # 写入数据
                    for entry in self.data_log:
                        data = entry['data']
                        calc = entry['calculated']
                        row = [
                            entry['timestamp'],
                            data.get('ax', 0), data.get('ay', 0), data.get('az', 0),
                            data.get('gx', 0), data.get('gy', 0), data.get('gz', 0),
                            data.get('angle_x', 0), data.get('angle_y', 0), data.get('angle_z', 0),
                            data.get('imu_type', 0), data.get('fall_state', 0),
                            calc.get('acc_mag', 0), calc.get('gyr_mag', 0)
                        ]
                        writer.writerow(row)

                self.log_message(f"📊 CSV已导出: {filename}", "green")
                messagebox.showinfo("成功", f"CSV数据已导出到:\n{filename}")
            except Exception as e:
                self.log_message(f"❌ 导出失败: {e}", "red")
                messagebox.showerror("错误", f"导出失败: {e}")

    def load_data(self):
        """加载历史数据"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 加载统计数据
                if 'statistics' in data:
                    stats = data['statistics']
                    self.fall_count = stats.get('fall_count', 0)
                    self.false_positive_count = stats.get('false_positive_count', 0)

                # 加载摔倒事件
                if 'fall_events' in data:
                    self.stats['fall_events'] = []
                    for event in data['fall_events']:
                        event['timestamp'] = datetime.fromisoformat(event['timestamp'])
                        self.stats['fall_events'].append(event)

                # 更新事件列表
                for item in self.events_tree.get_children():
                    self.events_tree.delete(item)

                for event in self.stats['fall_events']:
                    time_str = event['timestamp'].strftime("%H:%M:%S.%f")[:-3]
                    self.events_tree.insert('', 0, values=(
                        time_str,
                        f"{event['acc_mag']:.3f}g",
                        f"{event['gyr_mag']:.3f}°/s",
                        f"{event['angles']['x']:.1f}°",
                        f"{event['angles']['y']:.1f}°"
                    ))

                self.log_message(f"📂 数据已加载: {filename}", "green")
                messagebox.showinfo("成功", f"数据已从以下文件加载:\n{filename}")
            except Exception as e:
                self.log_message(f"❌ 加载失败: {e}", "red")
                messagebox.showerror("错误", f"加载失败: {e}")

    def clear_all_data(self):
        """清除所有数据"""
        if messagebox.askyesno("确认", "确定要清除所有数据吗？这将删除所有测试记录！"):
            self.data_log.clear()
            for key in self.real_time_data:
                self.real_time_data[key].clear()
            self.reset_stats()
            self.log_text.delete(1.0, tk.END)
            self.log_message("🗑️ 所有数据已清除", "blue")

    def auto_save_data(self):
        """自动保存数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"auto_save_{timestamp}.json"

        # 简化的自动保存数据
        auto_save_data = {
            'timestamp': timestamp,
            'fall_count': self.fall_count,
            'false_positive_count': self.false_positive_count,
            'recent_events': self.stats['fall_events'][-10:] if self.stats['fall_events'] else []
        }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(auto_save_data, f, indent=2, default=str)
        except Exception as e:
            self.log_message(f"自动保存失败: {e}", "red")

    def clear_log(self):
        """清除日志显示"""
        if self.log_text:
            self.log_text.delete(1.0, tk.END)
        self.log_message("🗑️ 日志已清除", "blue")

    def log_message(self, message, color="black"):
        """添加日志消息"""
        if not self.log_text:
            return

        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)

        # 添加颜色标记
        if color != "black":
            start_line = self.log_text.index(tk.END + "-2l")
            end_line = self.log_text.index(tk.END + "-1l")
            self.log_text.tag_add(color, start_line, end_line)
            self.log_text.tag_config(color, foreground=color)

        # 自动滚动到底部
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 1000:
            self.log_text.delete(1.0, "500.0")

    def play_alert_sound(self):
        """播放报警声音"""
        try:
            if self.sound_enabled:
                # Windows系统声音
                winsound.Beep(1000, 500)  # 1000Hz, 500ms
                threading.Thread(target=lambda: [
                    time.sleep(0.6),
                    winsound.Beep(1200, 300),
                    time.sleep(0.4),
                    winsound.Beep(1000, 500)
                ], daemon=True).start()
        except Exception as e:
            self.log_message(f"声音播放失败: {e}", "orange")

    def on_closing(self):
        """程序关闭处理"""
        if messagebox.askokcancel("退出", "确定要退出测试工具吗？"):
            self.running = False
            if self.mqtt_client:
                self.mqtt_client.loop_stop()
                self.mqtt_client.disconnect()
            self.root.destroy()

    def run(self):
        """运行测试工具"""
        self.log_message("🚀 ESP32S3摔倒检测详细测试工具启动", "green")
        self.log_message(f"📡 MQTT配置: {self.mqtt_broker}:{self.mqtt_port}", "blue")
        self.log_message(f"📋 监听主题: {self.mqtt_topic}", "blue")
        self.log_message("💡 请在'测试控制'标签页中点击'开始测试'按钮", "blue")
        self.log_message("📖 使用说明请查看各个标签页的功能", "blue")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
        finally:
            self.running = False

if __name__ == "__main__":
    print("启动ESP32S3摔倒检测详细测试工具...")
    print("请确保已安装所需依赖: pip install paho-mqtt matplotlib numpy")

    try:
        tester = FallDetectionTester()
        tester.run()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
        
    def setup_mqtt(self):
        """设置MQTT连接"""
        self.mqtt_client = mqtt.Client()
        self.mqtt_client.on_connect = self.on_mqtt_connect
        self.mqtt_client.on_message = self.on_mqtt_message
        self.mqtt_client.on_disconnect = self.on_mqtt_disconnect
        
    def on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            self.connected = True
            self.connection_label.config(text="已连接", foreground="green")
            client.subscribe(self.mqtt_topic)
            self.log_message("MQTT连接成功，开始监听数据...")
        else:
            self.log_message(f"MQTT连接失败，错误码: {rc}")
            
    def on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        self.connected = False
        self.connection_label.config(text="连接断开", foreground="red")
        self.log_message("MQTT连接断开")
        
    def on_mqtt_message(self, client, userdata, msg):
        """接收MQTT消息"""
        try:
            data = json.loads(msg.payload.decode())
            self.process_imu_data(data)
        except Exception as e:
            self.log_message(f"数据解析错误: {e}")
            
    def process_imu_data(self, data):
        """处理IMU数据"""
        self.imu_data = data
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        # 更新GUI显示
        self.update_gui_data(data)
        
        # 检查摔倒状态
        fall_state = data.get('fall_state', 0)
        if fall_state == 3:  # 摔倒确认
            self.fall_count += 1
            self.status_labels['fall_count'].config(text=str(self.fall_count))
            self.log_message(f"🚨 摔倒检测！第{self.fall_count}次", "red")
            self.play_alert_sound()
            
        # 记录数据
        log_entry = {
            'timestamp': timestamp,
            'data': data.copy()
        }
        self.data_log.append(log_entry)
        
        # 限制日志长度
        if len(self.data_log) > 1000:
            self.data_log = self.data_log[-500:]
            
    def update_gui_data(self, data):
        """更新GUI数据显示"""
        # 更新IMU数值
        for key in ['ax', 'ay', 'az', 'gx', 'gy', 'gz', 'angle_x', 'angle_y', 'angle_z']:
            if key in data and key in self.status_labels:
                value = data[key]
                self.status_labels[key].config(text=f"{value:.3f}")
                
        # 更新运动状态
        motion_state = data.get('imu_type', 0)
        motion_text = self.motion_states.get(motion_state, "未知")
        color = "green" if motion_state == 0 else "orange" if motion_state < 3 else "red"
        self.status_labels['motion'].config(text=motion_text, foreground=color)
        
        # 更新摔倒状态
        fall_state = data.get('fall_state', 0)
        fall_text = self.fall_states.get(fall_state, "未知")
        if fall_state == 0:
            color = "green"
        elif fall_state == 1:
            color = "orange"
        elif fall_state == 2:
            color = "red"
        else:  # fall_state == 3
            color = "purple"
        self.status_labels['fall'].config(text=fall_text, foreground=color)
        
    def log_message(self, message, color="black"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if self.log_text:
            self.log_text.insert(tk.END, log_entry)
            if color != "black":
                # 为特殊消息添加颜色标记
                start_line = self.log_text.index(tk.END + "-2l")
                end_line = self.log_text.index(tk.END + "-1l")
                self.log_text.tag_add(color, start_line, end_line)
                self.log_text.tag_config(color, foreground=color)
            self.log_text.see(tk.END)
            
    def play_alert_sound(self):
        """播放报警声音"""
        try:
            # Windows系统声音
            winsound.Beep(1000, 500)  # 1000Hz, 500ms
        except:
            pass  # 忽略声音播放错误
            
    def start_test(self):
        """开始测试"""
        try:
            self.mqtt_client.connect(self.mqtt_broker, self.mqtt_port, 60)
            self.mqtt_client.loop_start()
            self.test_start_time = datetime.now()
            self.fall_count = 0
            self.status_labels['fall_count'].config(text="0")
            self.log_message("测试开始...")
        except Exception as e:
            self.log_message(f"连接失败: {e}", "red")
            
    def stop_test(self):
        """停止测试"""
        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
        self.log_message("测试停止")
        
    def save_log(self):
        """保存测试日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"fall_test_log_{timestamp}.json"
        
        test_summary = {
            'test_start_time': self.test_start_time.isoformat(),
            'test_end_time': datetime.now().isoformat(),
            'fall_count': self.fall_count,
            'total_samples': len(self.data_log),
            'data_log': self.data_log
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, indent=2, ensure_ascii=False)
            self.log_message(f"日志已保存到: {filename}", "blue")
        except Exception as e:
            self.log_message(f"保存失败: {e}", "red")
            
    def clear_log(self):
        """清除日志"""
        if self.log_text:
            self.log_text.delete(1.0, tk.END)
        self.data_log.clear()
        self.log_message("日志已清除")
        
    def run(self):
        """运行测试工具"""
        self.log_message("摔倒检测测试工具启动")
        self.log_message(f"MQTT服务器: {self.mqtt_broker}:{self.mqtt_port}")
        self.log_message(f"监听主题: {self.mqtt_topic}")
        self.log_message("请点击'开始测试'按钮连接设备")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.stop_test()
            
if __name__ == "__main__":
    # 创建测试工具实例并运行
    tester = FallDetectionTester()
    tester.run()
