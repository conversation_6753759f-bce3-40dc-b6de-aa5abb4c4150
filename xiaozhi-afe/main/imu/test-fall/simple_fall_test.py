#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32S3摔倒检测简化测试工具
"""

import json
import time
import threading
from datetime import datetime
import paho.mqtt.client as mqtt
import tkinter as tk
from tkinter import ttk, messagebox
import queue

class SimpleFallDetectionTest:
    def __init__(self):
        # MQTT配置
        self.mqtt_broker = "192.168.2.101"
        self.mqtt_port = 1883
        self.mqtt_topic = "doll/imu_status"
        
        # 数据
        self.mqtt_client = None
        self.connected = False
        self.message_queue = queue.Queue()
        self.running = True
        
        # 统计数据
        self.total_messages = 0
        self.fall_count = 0
        self.last_fall_time = None
        self.start_time = datetime.now()
        
        # 摔倒状态映射
        self.fall_states = {
            0: "正常",
            1: "冲击检测", 
            2: "确认中",
            3: "摔倒确认"
        }
        
        # 运动状态映射
        self.motion_states = {
            0: "静止",
            1: "轻微运动",
            2: "中等运动", 
            3: "剧烈运动"
        }
        
        self.setup_gui()
        self.setup_mqtt()
        
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("ESP32S3摔倒检测简化测试工具")
        self.root.geometry("600x500")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接状态
        status_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="5")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_frame, text="MQTT服务器:").grid(row=0, column=0, sticky=tk.W)
        self.server_label = ttk.Label(status_frame, text=f"{self.mqtt_broker}:{self.mqtt_port}")
        self.server_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="连接状态:").grid(row=1, column=0, sticky=tk.W)
        self.connection_label = ttk.Label(status_frame, text="未连接", foreground="red")
        self.connection_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 实时数据
        data_frame = ttk.LabelFrame(main_frame, text="实时数据", padding="5")
        data_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 加速度
        ttk.Label(data_frame, text="加速度 (g):").grid(row=0, column=0, sticky=tk.W)
        self.acc_label = ttk.Label(data_frame, text="X: 0.000  Y: 0.000  Z: 0.000")
        self.acc_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 角速度
        ttk.Label(data_frame, text="角速度 (°/s):").grid(row=1, column=0, sticky=tk.W)
        self.gyro_label = ttk.Label(data_frame, text="X: 0.000  Y: 0.000  Z: 0.000")
        self.gyro_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 姿态角
        ttk.Label(data_frame, text="姿态角 (°):").grid(row=2, column=0, sticky=tk.W)
        self.angle_label = ttk.Label(data_frame, text="X: 0.000  Y: 0.000  Z: 0.000")
        self.angle_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 状态信息
        status_data_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="5")
        status_data_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_data_frame, text="运动状态:").grid(row=0, column=0, sticky=tk.W)
        self.motion_label = ttk.Label(status_data_frame, text="静止", foreground="green")
        self.motion_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_data_frame, text="摔倒状态:").grid(row=1, column=0, sticky=tk.W)
        self.fall_label = ttk.Label(status_data_frame, text="正常", foreground="green")
        self.fall_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 统计信息
        stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="5")
        stats_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(stats_frame, text="运行时间:").grid(row=0, column=0, sticky=tk.W)
        self.runtime_label = ttk.Label(stats_frame, text="00:00:00")
        self.runtime_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(stats_frame, text="接收消息数:").grid(row=1, column=0, sticky=tk.W)
        self.message_count_label = ttk.Label(stats_frame, text="0")
        self.message_count_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(stats_frame, text="摔倒次数:").grid(row=2, column=0, sticky=tk.W)
        self.fall_count_label = ttk.Label(stats_frame, text="0")
        self.fall_count_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(stats_frame, text="最后摔倒:").grid(row=3, column=0, sticky=tk.W)
        self.last_fall_label = ttk.Label(stats_frame, text="无")
        self.last_fall_label.grid(row=3, column=1, sticky=tk.W, padx=(10, 0))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))
        
        self.connect_button = ttk.Button(button_frame, text="连接", command=self.toggle_connection)
        self.connect_button.grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(button_frame, text="重置统计", command=self.reset_stats).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(button_frame, text="退出", command=self.on_closing).grid(row=0, column=2)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        # 创建滚动文本框
        self.log_text = tk.Text(log_frame, height=8, width=70)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_mqtt(self):
        """设置MQTT连接"""
        self.mqtt_client = mqtt.Client()
        self.mqtt_client.on_connect = self.on_mqtt_connect
        self.mqtt_client.on_message = self.on_mqtt_message
        self.mqtt_client.on_disconnect = self.on_mqtt_disconnect
        
    def on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            self.connected = True
            self.connection_label.config(text="已连接", foreground="green")
            client.subscribe(self.mqtt_topic)
            self.log_message("✅ MQTT连接成功")
            self.connect_button.config(text="断开")
        else:
            self.connected = False
            self.connection_label.config(text="连接失败", foreground="red")
            self.log_message(f"❌ MQTT连接失败，错误码: {rc}")
            
    def on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        self.connected = False
        self.connection_label.config(text="连接断开", foreground="red")
        self.connect_button.config(text="连接")
        self.log_message("⚠️ MQTT连接断开")
        
    def on_mqtt_message(self, client, userdata, msg):
        """接收MQTT消息"""
        try:
            payload_str = msg.payload.decode()
            print(f"🔍 简化测试工具收到MQTT消息: {len(payload_str)} chars")
            print(f"   主题: {msg.topic}")
            print(f"   内容: {payload_str[:100]}{'...' if len(payload_str) > 100 else ''}")

            data = json.loads(payload_str)
            self.message_queue.put(data)
            print(f"✅ 数据已放入队列，队列大小: {self.message_queue.qsize()}")
        except Exception as e:
            print(f"❌ 数据解析错误: {e}")
            print(f"   原始数据: {msg.payload}")
            self.log_message(f"❌ 数据解析错误: {e}")
            
    def process_imu_data(self, data):
        """处理IMU数据"""
        self.total_messages += 1
        
        # 更新实时数据显示
        ax = data.get('ax', 0)
        ay = data.get('ay', 0)
        az = data.get('az', 0)
        gx = data.get('gx', 0)
        gy = data.get('gy', 0)
        gz = data.get('gz', 0)
        angle_x = data.get('angle_x', 0)
        angle_y = data.get('angle_y', 0)
        angle_z = data.get('angle_z', 0)
        
        self.acc_label.config(text=f"X: {ax:.3f}  Y: {ay:.3f}  Z: {az:.3f}")
        self.gyro_label.config(text=f"X: {gx:.1f}  Y: {gy:.1f}  Z: {gz:.1f}")
        self.angle_label.config(text=f"X: {angle_x:.1f}  Y: {angle_y:.1f}  Z: {angle_z:.1f}")
        
        # 更新状态显示
        motion_state = data.get('imu_type', 0)
        fall_state = data.get('fall_state', 0)
        
        motion_text = self.motion_states.get(motion_state, "未知")
        fall_text = self.fall_states.get(fall_state, "未知")
        
        # 运动状态颜色
        motion_color = "green" if motion_state == 0 else "orange" if motion_state <= 2 else "red"
        self.motion_label.config(text=motion_text, foreground=motion_color)
        
        # 摔倒状态颜色和处理
        if fall_state == 0:
            fall_color = "green"
        elif fall_state == 1:
            fall_color = "orange"
        elif fall_state == 2:
            fall_color = "red"
        else:  # fall_state == 3
            fall_color = "purple"
            # 摔倒确认
            self.fall_count += 1
            self.last_fall_time = datetime.now()
            self.log_message(f"🚨 摔倒检测！第{self.fall_count}次", "red")
            
        self.fall_label.config(text=fall_text, foreground=fall_color)
        
        # 更新统计信息
        self.update_stats()
        
    def update_stats(self):
        """更新统计信息"""
        # 运行时间
        runtime = datetime.now() - self.start_time
        hours, remainder = divmod(int(runtime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        self.runtime_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
        
        # 消息数和摔倒次数
        self.message_count_label.config(text=str(self.total_messages))
        self.fall_count_label.config(text=str(self.fall_count))
        
        # 最后摔倒时间
        if self.last_fall_time:
            last_fall_str = self.last_fall_time.strftime("%H:%M:%S")
            self.last_fall_label.config(text=last_fall_str)
        
    def toggle_connection(self):
        """切换连接状态"""
        if self.connected:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
        else:
            try:
                self.mqtt_client.connect(self.mqtt_broker, self.mqtt_port, 60)
                self.mqtt_client.loop_start()
                self.log_message("🔄 正在连接MQTT服务器...")
            except Exception as e:
                self.log_message(f"❌ 连接失败: {e}")
                
    def reset_stats(self):
        """重置统计信息"""
        self.total_messages = 0
        self.fall_count = 0
        self.last_fall_time = None
        self.start_time = datetime.now()
        self.log_message("🔄 统计信息已重置")
        
    def log_message(self, message, color="black"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 100:
            self.log_text.delete(1.0, "2.0")
            
    def data_update_loop(self):
        """数据更新循环"""
        print("🔄 简化测试工具数据更新循环启动")
        while self.running:
            try:
                # 处理MQTT消息队列
                queue_size = self.message_queue.qsize()
                if queue_size > 0:
                    print(f"📥 队列中有 {queue_size} 条消息待处理")

                while not self.message_queue.empty():
                    data = self.message_queue.get_nowait()
                    print(f"🔄 处理IMU数据: {data.get('device_id', 'unknown')}")
                    self.process_imu_data(data)

                # 定期更新统计信息（即使没有新数据）
                self.root.after_idle(self.update_stats)

                time.sleep(0.05)  # 20Hz更新频率
            except Exception as e:
                print(f"数据更新错误: {e}")
                
    def on_closing(self):
        """程序关闭处理"""
        if messagebox.askokcancel("退出", "确定要退出测试工具吗？"):
            self.running = False
            if self.mqtt_client and self.connected:
                self.mqtt_client.loop_stop()
                self.mqtt_client.disconnect()
            self.root.destroy()
            
    def run(self):
        """运行测试工具"""
        # 启动数据更新线程
        update_thread = threading.Thread(target=self.data_update_loop, daemon=True)
        update_thread.start()
        
        self.log_message("🚀 ESP32S3摔倒检测简化测试工具启动")
        self.log_message(f"📡 MQTT服务器: {self.mqtt_broker}:{self.mqtt_port}")
        self.log_message(f"📋 监听主题: {self.mqtt_topic}")
        
        # 启动GUI
        self.root.mainloop()

def main():
    app = SimpleFallDetectionTest()
    app.run()

if __name__ == "__main__":
    main()
