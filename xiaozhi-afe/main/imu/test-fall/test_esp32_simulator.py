#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32S3数据模拟器 - 用于测试增强MQTT服务器
"""

import socket
import json
import time
import random
import threading

class ESP32Simulator:
    def __init__(self, server_host='*************', server_port=1883):
        self.server_host = server_host
        self.server_port = server_port
        self.running = False
        self.socket = None
        self.fall_triggered = False
        
    def connect(self):
        """连接到MQTT服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.server_host, self.server_port))
            
            # 发送MQTT CONNECT消息
            client_id = "ESP32_Simulator"
            connect_msg = self.build_connect_message(client_id)
            self.socket.send(connect_msg)
            
            # 等待CONNACK
            response = self.socket.recv(1024)
            if len(response) >= 4 and response[0] == 0x20:
                print(f"✅ 连接到MQTT服务器成功: {self.server_host}:{self.server_port}")
                return True
            else:
                print("❌ MQTT连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def build_connect_message(self, client_id):
        """构建MQTT CONNECT消息"""
        # MQTT固定头部
        fixed_header = bytes([0x10])  # CONNECT
        
        # 可变头部
        protocol_name = "MQTT"
        protocol_level = 4
        connect_flags = 0x02  # Clean Session
        keep_alive = 60
        
        variable_header = (
            bytes([0x00, len(protocol_name)]) + protocol_name.encode() +
            bytes([protocol_level, connect_flags]) +
            bytes([keep_alive >> 8, keep_alive & 0xFF])
        )
        
        # 载荷
        payload = bytes([0x00, len(client_id)]) + client_id.encode()
        
        # 计算剩余长度
        remaining_length = len(variable_header) + len(payload)
        fixed_header += bytes([remaining_length])
        
        return fixed_header + variable_header + payload
    
    def build_publish_message(self, topic, payload):
        """构建MQTT PUBLISH消息"""
        # 固定头部
        fixed_header = bytes([0x30])  # PUBLISH, QoS=0
        
        # 可变头部（主题）
        topic_bytes = topic.encode()
        variable_header = bytes([len(topic_bytes) >> 8, len(topic_bytes) & 0xFF]) + topic_bytes
        
        # 载荷
        payload_bytes = payload.encode()
        
        # 计算剩余长度
        remaining_length = len(variable_header) + len(payload_bytes)
        fixed_header += bytes([remaining_length])
        
        return fixed_header + variable_header + payload_bytes
    
    def generate_imu_data(self):
        """生成模拟IMU数据"""
        if self.fall_triggered:
            # 模拟摔倒数据
            data = {
                "device_id": "ESP32_Simulator",
                "ax": random.uniform(2.0, 4.0),  # 大加速度
                "ay": random.uniform(1.0, 3.0),
                "az": random.uniform(0.5, 2.0),
                "gx": random.uniform(150.0, 250.0),  # 大角速度
                "gy": random.uniform(100.0, 200.0),
                "gz": random.uniform(80.0, 150.0),
                "angle_x": random.uniform(30.0, 60.0),  # 大角度变化
                "angle_y": random.uniform(20.0, 50.0),
                "angle_z": random.uniform(10.0, 30.0),
                "imu_type": 3,  # 剧烈运动
                "fall_state": 3,  # 摔倒确认
                "touch_value": 0
            }
            self.fall_triggered = False  # 重置摔倒标志
        else:
            # 模拟正常数据
            data = {
                "device_id": "ESP32_Simulator",
                "ax": random.uniform(-0.2, 0.2),
                "ay": random.uniform(-0.2, 0.2),
                "az": random.uniform(0.8, 1.2),  # 重力加速度
                "gx": random.uniform(-5.0, 5.0),
                "gy": random.uniform(-5.0, 5.0),
                "gz": random.uniform(-5.0, 5.0),
                "angle_x": random.uniform(-5.0, 5.0),
                "angle_y": random.uniform(-5.0, 5.0),
                "angle_z": random.uniform(-5.0, 5.0),
                "imu_type": random.randint(0, 1),  # 静止或轻微运动
                "fall_state": 0,  # 正常状态
                "touch_value": 0
            }
        
        return data
    
    def send_data_loop(self):
        """数据发送循环"""
        topic = "doll/imu_status"
        counter = 0
        
        while self.running:
            try:
                # 生成IMU数据
                imu_data = self.generate_imu_data()
                payload = json.dumps(imu_data)
                
                # 构建并发送PUBLISH消息
                publish_msg = self.build_publish_message(topic, payload)
                self.socket.send(publish_msg)
                
                counter += 1
                
                # 每50次数据（约2秒）触发一次摔倒
                if counter % 50 == 0:
                    self.fall_triggered = True
                    print(f"🧪 模拟摔倒事件 (第 {counter//50} 次)")
                
                time.sleep(0.04)  # 25Hz发送频率
                
            except Exception as e:
                print(f"❌ 发送数据错误: {e}")
                break
    
    def start(self):
        """启动模拟器"""
        if not self.connect():
            return False
        
        self.running = True
        
        # 启动数据发送线程
        send_thread = threading.Thread(target=self.send_data_loop, daemon=True)
        send_thread.start()
        
        print("🚀 ESP32模拟器启动成功")
        print("📊 数据发送频率: 25Hz")
        print("🚨 每2秒模拟一次摔倒事件")
        print("⏹️  按Ctrl+C停止模拟器")
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️  停止模拟器...")
            self.stop()
        
        return True
    
    def stop(self):
        """停止模拟器"""
        self.running = False
        if self.socket:
            self.socket.close()
        print("✅ 模拟器已停止")

def main():
    print("=" * 50)
    print("🧪 ESP32S3数据模拟器")
    print("=" * 50)
    print("用途: 测试增强MQTT服务器的数据接收和转发功能")
    print()
    
    simulator = ESP32Simulator()
    simulator.start()

if __name__ == "__main__":
    main()
