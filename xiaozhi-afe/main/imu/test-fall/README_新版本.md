# ESP32S3摔倒检测测试工具 - 新版本说明

## 🎯 问题解决

### ✅ 已修复的问题
1. **测试工具收不到数据** - 新的增强MQTT服务器支持真正的MQTT协议转发
2. **数据打印频率过高** - 改为1秒打印一条统计信息
3. **摔倒检测不明显** - 添加了醒目的摔倒警报显示
4. **数据转发延迟** - 实现实时数据转发

## 🚀 新功能特性

### 📡 增强MQTT服务器 (`mqtt_server_enhanced.py`)
- **智能数据统计**: 1秒打印一条数据统计，避免刷屏
- **实时数据转发**: 收到ESP32数据后立即转发给测试工具
- **摔倒警报显示**: 检测到摔倒时显示详细的警报信息
- **多客户端支持**: 支持多个测试工具同时连接
- **连接状态监控**: 实时显示连接的设备数量

### 🔧 改进的测试工具
- **配置优化**: 更新了MQTT连接配置
- **兼容性增强**: 与新服务器完全兼容

## 📋 使用方法

### 方法1: 使用批处理文件（推荐）
1. **启动服务器**: 双击 `启动增强服务器.bat`
2. **启动测试工具**: 双击 `启动.bat`，选择选项1

### 方法2: 使用Python脚本
1. **启动服务器**: 
   ```bash
   python mqtt_server_enhanced.py
   ```
2. **启动测试工具**: 
   ```bash
   python fall_detection_test.py
   ```

### 方法3: 使用新的启动器
```bash
python start_new.py
```
然后选择相应的选项。

## 📊 服务器输出示例

### 正常运行时
```
📊 [14:30:15] 数据统计: 收到 25 条IMU数据 | 摔倒总数: 0 | 连接的测试工具: 1
📊 [14:30:16] 数据统计: 收到 24 条IMU数据 | 摔倒总数: 0 | 连接的测试工具: 1
```

### 摔倒检测时
```
🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨
🚨🚨🚨 摔倒检测警报！第 1 次 🚨🚨🚨
🚨 设备IP: *************
🚨 检测时间: 14:30:25.123
🚨 设备ID: ESP32_001
🚨 加速度: X=2.456g Y=1.234g Z=0.789g
🚨 角速度: X=180.5°/s Y=120.3°/s Z=95.7°/s
🚨 姿态角: X=45.2° Y=30.1° Z=15.8°
🚨 运动状态: 3
🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨
```

## 🌐 网络配置

### ESP32S3配置
确保ESP32S3连接到与电脑相同的WiFi网络，MQTT配置：
```cpp
const char* mqtt_server = "*************";
const int mqtt_port = 1883;
const char* mqtt_topic = "doll/imu_status";
```

### 测试工具配置
配置文件 `config.json` 已自动更新：
```json
{
  "mqtt": {
    "broker": "*************",
    "port": 1883,
    "topic": "doll/imu_status",
    "client_id": "fall_detection_test_tool"
  }
}
```

## 🔧 故障排除

### 问题1: 测试工具仍然收不到数据
**解决方案**:
1. 确保先启动增强MQTT服务器
2. 再启动测试工具
3. 检查两者是否连接到同一个IP地址

### 问题2: ESP32连接不上服务器
**解决方案**:
1. 检查ESP32和电脑是否在同一WiFi网络
2. 确认IP地址 ************* 是否正确
3. 检查防火墙是否阻止了1883端口

### 问题3: 端口被占用
**解决方案**:
1. 关闭其他MQTT服务器（如mosquitto）
2. 或者修改配置使用其他端口

## 📁 文件说明

- `mqtt_server_enhanced.py` - 新的增强MQTT服务器
- `start_new.py` - 新的启动器脚本
- `启动增强服务器.bat` - 服务器启动批处理文件
- `fall_detection_test.py` - 测试工具（无需修改）
- `config.json` - 配置文件（已更新）

## 🔄 数据流程

```
ESP32S3 → 增强MQTT服务器 → 测试工具
         ↓
    1秒统计打印
    摔倒警报显示
```

## 💡 使用建议

1. **开发调试**: 使用增强MQTT服务器，可以看到详细的数据统计和摔倒警报
2. **正式测试**: 同时运行服务器和测试工具，获得完整的测试数据
3. **数据分析**: 使用测试工具保存的JSON日志进行后续分析

## 📞 技术支持

如果遇到问题，请检查：
1. Python依赖包是否安装完整
2. 网络连接是否正常
3. 防火墙设置是否正确
4. ESP32S3固件是否正确配置
