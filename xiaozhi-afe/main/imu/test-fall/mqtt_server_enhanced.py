#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32S3摔倒检测 - 增强MQTT服务器
功能：
1. 接收ESP32数据
2. 1秒打印一条统计信息
3. 实时转发数据给测试工具
4. 摔倒检测标志打印
"""

import os
import sys
import socket
import subprocess
import threading
import time
import json
import queue
from datetime import datetime

def get_local_ip():
    """获取本机IP地址"""
    return "*************"

def check_dependencies():
    """检查并安装依赖"""
    print("🔍 检查Python依赖包...")
    
    required_packages = ['paho-mqtt']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 需要安装依赖包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败，请手动运行: pip install paho-mqtt")
            return False
    else:
        print("✅ 所有依赖包已安装")
    
    return True

class EnhancedMQTTServer:
    def __init__(self, host='0.0.0.0', port=1883):
        self.host = host
        self.port = port
        self.running = True
        self.last_print_time = 0
        self.data_count = 0
        self.fall_count = 0
        self.clients = []  # 存储连接的测试工具客户端
        self.data_queue = queue.Queue()
        
        # 启动数据转发线程
        self.forward_thread = threading.Thread(target=self.data_forward_loop, daemon=True)
        self.forward_thread.start()
        
    def start(self):
        try:
            self.server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server.bind((self.host, self.port))
            self.server.listen(10)
            
            local_ip = get_local_ip()
            
            print("=" * 60)
            print("🚀 ESP32S3摔倒检测 - 增强MQTT服务器 v2.0")
            print("=" * 60)
            print(f"✅ 服务器启动成功")
            print(f"📡 监听端口: {self.port}")
            print(f"🌐 服务器地址: {local_ip}:{self.port}")
            print(f"📋 MQTT主题: doll/imu_status")
            print(f"🔄 数据转发: 启用")
            print(f"📊 打印频率: 1秒统计一次")
            print("-" * 60)
            print("💡 ESP32S3配置:")
            print(f'   MQTT服务器: {local_ip}')
            print(f'   MQTT端口: {self.port}')
            print(f'   发布主题: doll/imu_status')
            print("-" * 60)
            print("💡 测试工具配置:")
            print(f'   MQTT服务器: {local_ip}')
            print(f'   MQTT端口: {self.port}')
            print(f'   订阅主题: doll/imu_status')
            print("-" * 60)
            print("⏹️  按Ctrl+C停止服务器")
            print("🔗 等待设备连接...")
            print()
            
            while self.running:
                try:
                    client_socket, addr = self.server.accept()
                    print(f"🔗 新设备连接: {addr[0]}:{addr[1]}")
                    
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, addr),
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.error:
                    if self.running:
                        print("❌ 接受连接失败")
                    break
                    
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
            if "Address already in use" in str(e):
                print("💡 端口1883已被占用，请先停止其他MQTT服务")
            return False
            
        return True
        
    def handle_client(self, client_socket, addr):
        """处理客户端连接"""
        client_type = "unknown"
        try:
            # 设置socket超时，避免阻塞
            client_socket.settimeout(30.0)

            while self.running:
                try:
                    data = client_socket.recv(4096)
                    if not data:
                        break

                    # MQTT协议处理
                    if data[0] == 0x10:  # CONNECT
                        # 解析客户端类型
                        client_type = self.parse_client_type(data)

                        # 发送CONNACK
                        connack = bytes([0x20, 0x02, 0x00, 0x00])
                        client_socket.send(connack)
                        print(f"✅ {addr[0]} 连接成功 ({client_type})")

                        # 如果是测试工具，添加到客户端列表
                        if client_type == "测试工具":
                            self.clients.append(client_socket)

                    elif (data[0] >> 4) == 3:  # PUBLISH
                        if client_type == "ESP32" or client_type == "unknown":
                            self.handle_esp32_publish(data, addr[0])

                        # 发送PUBACK
                        if len(data) >= 4:
                            qos = (data[0] >> 1) & 0x03
                            if qos > 0:
                                puback = bytes([0x40, 0x02, data[2], data[3]])
                                client_socket.send(puback)

                    elif data[0] == 0x82:  # SUBSCRIBE
                        # 发送SUBACK
                        if len(data) >= 4:
                            suback = bytes([0x90, 0x03, data[2], data[3], 0x00])
                            client_socket.send(suback)
                            print(f"📋 {addr[0]} 订阅成功")

                    elif data[0] == 0xC0:  # PINGREQ
                        # 发送PINGRESP
                        pingresp = bytes([0xD0, 0x00])
                        client_socket.send(pingresp)

                except socket.timeout:
                    print(f"⏰ {addr[0]} 连接超时，但保持连接")
                    continue
                except ConnectionResetError:
                    print(f"🔌 {addr[0]} 主动断开连接")
                    break
                except Exception as recv_error:
                    print(f"⚠️ 接收数据错误 from {addr[0]}: {recv_error}")
                    break

        except Exception as e:
            print(f"⚠️ 客户端处理错误 {addr[0]}: {e}")
            import traceback
            traceback.print_exc()
        finally:
            if client_socket in self.clients:
                self.clients.remove(client_socket)
            try:
                client_socket.close()
            except:
                pass
            print(f"🔌 设备断开: {addr[0]} ({client_type})")
            
    def parse_client_type(self, data):
        """解析客户端类型"""
        try:
            # 跳过固定头部和剩余长度
            offset = 2
            if len(data) < offset + 10:
                return "unknown"

            # 跳过协议名长度和协议名 "MQTT" (6字节)
            protocol_name_len = (data[offset] << 8) | data[offset + 1]
            offset += 2 + protocol_name_len

            # 跳过协议级别、连接标志、保持活动时间 (4字节)
            offset += 4

            if len(data) < offset + 2:
                return "unknown"

            # 读取客户端ID长度
            client_id_len = (data[offset] << 8) | data[offset + 1]
            offset += 2

            if len(data) < offset + client_id_len:
                return "unknown"

            # 读取客户端ID
            client_id = data[offset:offset + client_id_len].decode('utf-8', errors='ignore')

            # 根据客户端ID判断类型
            if "esp32" in client_id.lower() or "doll" in client_id.lower() or len(client_id) < 15:
                return "ESP32"
            elif "test" in client_id.lower() or "fall" in client_id.lower():
                return "测试工具"
            else:
                return "unknown"

        except Exception as e:
            return "unknown"
            
    def handle_esp32_publish(self, data, client_ip):
        """处理ESP32发布的数据"""
        try:
            self.data_count += 1
            current_time = time.time()

            # 解析MQTT PUBLISH消息
            payload = self.extract_mqtt_payload(data)
            if payload:
                try:
                    json_data = json.loads(payload)

                    # 检查摔倒状态
                    fall_state = json_data.get('fall_state', 0)
                    if fall_state == 3:  # 摔倒确认
                        self.fall_count += 1
                        self.print_fall_alert(json_data, client_ip, current_time)

                    # 将数据加入转发队列
                    self.data_queue.put(json_data)

                except json.JSONDecodeError:
                    pass

            # 1秒打印一条统计信息
            if current_time - self.last_print_time >= 1.0:
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"📊 [{timestamp}] 收到 {self.data_count} 条数据 | 摔倒: {self.fall_count} 次 | 测试工具: {len(self.clients)} 个")
                self.last_print_time = current_time
                self.data_count = 0

        except Exception as e:
            print(f"⚠️ 数据处理错误: {e}")
            
    def extract_mqtt_payload(self, data):
        """提取MQTT消息的payload"""
        try:
            if len(data) < 4:
                return None

            # 解析固定头部
            msg_type = (data[0] >> 4) & 0x0F
            if msg_type != 3:  # 不是PUBLISH消息
                return None

            # 解析剩余长度
            remaining_length = data[1]
            offset = 2

            # 处理多字节长度编码
            if remaining_length & 0x80:
                remaining_length = remaining_length & 0x7F
                if len(data) > offset:
                    remaining_length += (data[offset] & 0x7F) << 7
                    offset += 1
                else:
                    return None

            # 获取主题长度
            if len(data) < offset + 2:
                return None

            topic_length = (data[offset] << 8) | data[offset + 1]
            offset += 2

            # 跳过主题
            payload_start = offset + topic_length

            if payload_start >= len(data):
                return None

            payload_bytes = data[payload_start:]
            payload = payload_bytes.decode('utf-8', errors='ignore')

            return payload

        except Exception as e:
            return None
        
    def print_fall_alert(self, json_data, client_ip, timestamp):
        """打印摔倒警报"""
        time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S.%f")[:-3]

        print("\n" + "=" * 80)
        print("🚨" * 30)
        print(f"🚨🚨🚨🚨🚨 摔倒检测警报！第 {self.fall_count} 次 🚨🚨🚨🚨🚨")
        print("🚨" * 30)
        print(f"⏰ 时间: {time_str}")
        print(f"� 设备: {client_ip} ({json_data.get('device_id', 'unknown')})")
        print(f"� 加速度: X={json_data.get('ax', 0):.3f}g  Y={json_data.get('ay', 0):.3f}g  Z={json_data.get('az', 0):.3f}g")
        print(f"� 角速度: X={json_data.get('gx', 0):.1f}°/s  Y={json_data.get('gy', 0):.1f}°/s  Z={json_data.get('gz', 0):.1f}°/s")
        print(f"� 姿态角: X={json_data.get('angle_x', 0):.1f}°  Y={json_data.get('angle_y', 0):.1f}°  Z={json_data.get('angle_z', 0):.1f}°")
        print(f"🏃 运动状态: {json_data.get('imu_type', 0)}")
        print("🚨" * 30)
        print("=" * 80 + "\n")
        
    def data_forward_loop(self):
        """数据转发循环"""
        while self.running:
            try:
                # 从队列获取数据（实时转发）
                json_data = self.data_queue.get(timeout=1.0)
                self.forward_to_clients(json_data)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"⚠️ 数据转发错误: {e}")
                
    def forward_to_clients(self, json_data):
        """转发数据到所有连接的测试工具客户端"""
        if not self.clients:
            return

        try:
            # 构建MQTT PUBLISH消息
            topic = "doll/imu_status"
            payload = json.dumps(json_data).encode('utf-8')

            # 构建MQTT PUBLISH包
            topic_bytes = topic.encode('utf-8')
            topic_len = len(topic_bytes)
            payload_len = len(payload)

            # 固定头部
            fixed_header = bytes([0x30])  # PUBLISH, QoS=0
            remaining_length = 2 + topic_len + payload_len

            # 处理大于127字节的剩余长度
            if remaining_length < 128:
                fixed_header += bytes([remaining_length])
            else:
                # 多字节编码
                byte1 = remaining_length & 0x7F | 0x80
                byte2 = (remaining_length >> 7) & 0x7F
                fixed_header += bytes([byte1, byte2])

            # 可变头部（主题）
            variable_header = bytes([topic_len >> 8, topic_len & 0xFF]) + topic_bytes

            # 完整消息
            message = fixed_header + variable_header + payload

            # 发送给所有客户端
            disconnected_clients = []

            for client in self.clients:
                try:
                    client.send(message)
                except Exception:
                    disconnected_clients.append(client)

            # 移除断开的客户端
            for client in disconnected_clients:
                self.clients.remove(client)

        except Exception as e:
            print(f"⚠️ 转发错误: {e}")
            
    def stop(self):
        """停止服务器"""
        self.running = False
        if hasattr(self, 'server'):
            self.server.close()

def main():
    print("=" * 60)
    print("🚀 ESP32S3摔倒检测 - 增强MQTT服务器启动器")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 显示网络信息
    local_ip = get_local_ip()
    print(f"🌐 本机IP地址: {local_ip}")
    print(f"📡 MQTT服务器地址: {local_ip}:1883")
    print(f"📋 MQTT主题: doll/imu_status")
    print()
    
    # 启动服务器
    server = EnhancedMQTTServer()
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n⏹️  正在停止服务器...")
        server.stop()
        print("✅ 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器运行错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
