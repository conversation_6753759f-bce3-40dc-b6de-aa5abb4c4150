#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT连接测试工具
用于验证测试工具能否连接到增强MQTT服务器
"""

import paho.mqtt.client as mqtt
import time
import json

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print("✅ 成功连接到增强MQTT服务器")
        print("📋 订阅主题: doll/imu_status")
        client.subscribe("doll/imu_status")
    else:
        print(f"❌ 连接失败，错误码: {rc}")

def on_message(client, userdata, msg):
    try:
        data = json.loads(msg.payload.decode())
        print(f"📨 收到数据: 设备ID={data.get('device_id', 'unknown')}, 摔倒状态={data.get('fall_state', 0)}")
    except:
        print(f"📨 收到原始数据: {msg.payload.decode()[:50]}...")

def on_disconnect(client, userdata, rc):
    print("🔌 与服务器断开连接")

def main():
    print("=" * 50)
    print("🔧 MQTT连接测试工具")
    print("=" * 50)
    print("目标服务器: 192.168.2.101:1883")
    print("订阅主题: doll/imu_status")
    print()
    
    # 创建MQTT客户端
    client = mqtt.Client(client_id="connection_test_tool")
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_disconnect = on_disconnect
    
    try:
        print("🔗 正在连接到增强MQTT服务器...")
        client.connect("192.168.2.101", 1883, 60)
        client.loop_start()
        
        print("⏰ 等待数据... (按Ctrl+C停止)")
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 停止测试")
    except Exception as e:
        print(f"❌ 连接错误: {e}")
    finally:
        client.loop_stop()
        client.disconnect()
        print("✅ 测试完成")

if __name__ == "__main__":
    main()
