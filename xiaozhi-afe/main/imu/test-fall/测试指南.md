# ESP32S3摔倒检测系统测试指南

## 🎯 修复内容总结

### ✅ 已解决的问题
1. **简易MQTT服务器能收到数据但测试工具收不到** ✅
2. **数据打印频率过高，需要1秒打印一条** ✅  
3. **摔倒检测时需要显示明显标志** ✅
4. **数据转发需要实时进行** ✅

### 🔧 技术改进
- **新增**: `mqtt_server_enhanced.py` - 增强MQTT服务器
- **改进**: 实现真正的MQTT协议转发
- **优化**: 1秒统计打印 + 实时数据转发
- **增强**: 摔倒检测醒目警报显示

## 🚀 快速测试步骤

### 步骤1: 启动增强MQTT服务器
```bash
# 方法1: 使用批处理文件
双击 "启动增强服务器.bat"

# 方法2: 使用Python命令
cd test-fall
python mqtt_server_enhanced.py
```

### 步骤2: 启动测试工具
```bash
# 新开一个终端窗口
cd test-fall
python fall_detection_test.py
```

### 步骤3: 模拟ESP32数据（可选）
```bash
# 如果没有真实ESP32设备，可以使用模拟器
cd test-fall
python test_esp32_simulator.py
```

## 📊 预期效果

### 增强MQTT服务器输出
```
🔗 新设备连接: *************:54321
✅ ************* MQTT连接成功 (ESP32)
🔗 新设备连接: *************:54322  
✅ ************* MQTT连接成功 (测试工具)
📋 ************* 订阅主题成功

📊 [14:30:15] 数据统计: 收到 25 条IMU数据 | 摔倒总数: 0 | 连接的测试工具: 1
📊 [14:30:16] 数据统计: 收到 24 条IMU数据 | 摔倒总数: 0 | 连接的测试工具: 1

🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨
🚨🚨🚨 摔倒检测警报！第 1 次 🚨🚨🚨
🚨 设备IP: *************
🚨 检测时间: 14:30:25.123
🚨 设备ID: ESP32_001
🚨 加速度: X=2.456g Y=1.234g Z=0.789g
🚨 角速度: X=180.5°/s Y=120.3°/s Z=95.7°/s
🚨 姿态角: X=45.2° Y=30.1° Z=15.8°
🚨 运动状态: 3
🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨
```

### 测试工具界面
- **连接状态**: 显示"已连接"
- **实时数据**: 显示ESP32发送的IMU数据
- **摔倒检测**: 检测到摔倒时播放声音并记录
- **数据图表**: 实时更新的数据曲线

## 🔧 完整测试流程

### 1. 环境准备
```bash
# 确保Python环境正常
python --version

# 检查依赖包
pip list | grep paho-mqtt
```

### 2. 启动服务器
```bash
cd test-fall
python mqtt_server_enhanced.py
```
**预期输出**: 服务器启动信息，显示监听端口1883

### 3. 启动测试工具
```bash
# 新开终端
cd test-fall  
python fall_detection_test.py
```
**预期结果**: 
- 测试工具GUI界面打开
- 连接状态显示"已连接"
- 服务器显示测试工具连接成功

### 4. 连接ESP32或启动模拟器
```bash
# 如果使用模拟器
cd test-fall
python test_esp32_simulator.py
```
**预期结果**:
- 服务器显示ESP32连接成功
- 开始显示数据统计信息
- 测试工具开始接收并显示数据

### 5. 验证摔倒检测
- **真实ESP32**: 物理摇晃或摔倒设备
- **模拟器**: 自动每2秒触发一次摔倒事件

**预期结果**:
- 服务器显示醒目的摔倒警报
- 测试工具播放声音并记录摔倒事件
- 图表显示摔倒状态变化

## 🐛 故障排除

### 问题1: 服务器启动失败 "Address already in use"
**原因**: 端口1883被占用
**解决**:
```bash
# 查找占用进程
netstat -ano | findstr :1883
# 或者
lsof -i :1883

# 杀死占用进程或重启电脑
```

### 问题2: 测试工具连接失败
**检查项**:
1. 服务器是否正常运行
2. IP地址是否正确 (*************)
3. 防火墙是否阻止连接
4. config.json配置是否正确

### 问题3: ESP32连接不上
**检查项**:
1. ESP32和电脑是否在同一WiFi网络
2. ESP32固件中的IP地址是否正确
3. 网络是否稳定

### 问题4: 数据不更新
**检查项**:
1. ESP32是否正常发送数据
2. 服务器是否显示数据统计
3. 测试工具是否正确订阅主题

## 📁 文件结构

```
test-fall/
├── mqtt_server_enhanced.py     # 新的增强MQTT服务器 ⭐
├── fall_detection_test.py      # 测试工具（原有）
├── test_esp32_simulator.py     # ESP32模拟器 ⭐
├── start_new.py               # 新的启动器 ⭐
├── config.json                # 配置文件（已更新）
├── 启动增强服务器.bat          # 服务器启动脚本 ⭐
├── 启动.bat                   # 原有启动脚本
├── README_新版本.md           # 新版本说明 ⭐
└── 测试指南.md                # 本文件 ⭐
```

## 🎯 测试重点

### 功能验证
- [ ] 服务器能正常启动并监听1883端口
- [ ] 测试工具能成功连接到服务器
- [ ] ESP32能成功连接并发送数据
- [ ] 服务器1秒打印一条统计信息
- [ ] 数据能实时转发到测试工具
- [ ] 摔倒检测时显示醒目警报
- [ ] 测试工具能正确接收和显示数据

### 性能验证  
- [ ] 数据转发延迟小于100ms
- [ ] 支持多个测试工具同时连接
- [ ] 长时间运行稳定性
- [ ] 内存使用合理

## 📞 技术支持

如果测试过程中遇到问题：

1. **查看日志**: 注意服务器和测试工具的输出信息
2. **检查网络**: 确保所有设备在同一网络
3. **重启测试**: 按顺序重启服务器和测试工具
4. **使用模拟器**: 排除ESP32硬件问题

## ✅ 测试完成标志

当看到以下现象时，说明系统工作正常：

1. ✅ 服务器显示设备连接成功
2. ✅ 每秒显示数据统计信息  
3. ✅ 摔倒时显示醒目警报
4. ✅ 测试工具实时更新数据
5. ✅ 测试工具能检测和记录摔倒事件
