#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的MQTT客户端测试工具
用于验证ESP32S3数据传输
"""

import paho.mqtt.client as mqtt
import json
import time

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print("✅ 连接MQTT服务器成功")
        client.subscribe("doll/imu_status")
        print("📋 已订阅主题: doll/imu_status")
    else:
        print(f"❌ 连接失败，错误码: {rc}")

def on_message(client, userdata, msg):
    try:
        payload = msg.payload.decode()
        print(f"\n📨 收到消息:")
        print(f"   主题: {msg.topic}")
        print(f"   长度: {len(payload)} chars")
        print(f"   内容: {payload}")
        
        # 尝试解析JSON
        data = json.loads(payload)
        print(f"✅ JSON解析成功:")
        print(f"   设备ID: {data.get('device_id', 'N/A')}")
        print(f"   加速度: X={data.get('ax', 0):.3f}g Y={data.get('ay', 0):.3f}g Z={data.get('az', 0):.3f}g")
        print(f"   角速度: X={data.get('gx', 0):.1f}°/s Y={data.get('gy', 0):.1f}°/s Z={data.get('gz', 0):.1f}°/s")
        print(f"   运动状态: {data.get('imu_type', 0)}")
        print(f"   摔倒状态: {data.get('fall_state', 0)}")
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        print(f"   原始数据: {payload}")
    except Exception as e:
        print(f"❌ 处理消息失败: {e}")

def on_disconnect(client, userdata, rc):
    print("⚠️ MQTT连接断开")

def main():
    print("🔍 MQTT客户端测试工具")
    print("=" * 40)
    
    client = mqtt.Client()
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_disconnect = on_disconnect
    
    try:
        print("🔄 正在连接MQTT服务器...")
        client.connect("192.168.2.101", 1883, 60)
        client.loop_start()
        
        print("⏳ 等待消息... (按Ctrl+C退出)")
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n👋 退出测试")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
    finally:
        client.loop_stop()
        client.disconnect()

if __name__ == "__main__":
    main()
