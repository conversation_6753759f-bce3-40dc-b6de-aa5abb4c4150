#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32S3摔倒检测测试工具启动脚本 - 新版本
"""

import os
import sys
import subprocess

def get_local_ip():
    """获取本机IP地址"""
    return "*************"

def start_enhanced_mqtt_server():
    """启动增强MQTT服务器"""
    print("🚀 启动增强MQTT服务器...")
    try:
        subprocess.run([sys.executable, 'mqtt_server_enhanced.py'])
    except FileNotFoundError:
        print("❌ 找不到 mqtt_server_enhanced.py 文件")
    except Exception as e:
        print(f"❌ 启动增强MQTT服务器失败: {e}")

def start_test_tool():
    """启动测试工具"""
    print("🔧 启动摔倒检测测试工具...")
    try:
        subprocess.run([sys.executable, 'fall_detection_test.py'])
    except FileNotFoundError:
        print("❌ 找不到 fall_detection_test.py 文件")
    except Exception as e:
        print(f"❌ 启动测试工具失败: {e}")

def start_data_analysis():
    """启动数据分析"""
    print("📊 数据分析工具")
    print("请选择要分析的JSON日志文件:")
    
    # 列出当前目录的JSON文件
    json_files = [f for f in os.listdir('.') if f.endswith('.json') and f != 'config.json']
    if json_files:
        print("可用的日志文件:")
        for i, file in enumerate(json_files, 1):
            print(f"  {i}. {file}")
        print()
        
        try:
            file_choice = int(input("请输入文件编号: ")) - 1
            if 0 <= file_choice < len(json_files):
                selected_file = json_files[file_choice]
                print(f"分析文件: {selected_file}")
                subprocess.run([sys.executable, 'analyze_data.py', selected_file])
            else:
                print("❌ 无效的文件编号")
        except ValueError:
            print("❌ 请输入有效的数字")
    else:
        print("❌ 当前目录没有找到JSON日志文件")
        print("💡 请先进行测试并保存数据")

def main():
    print("=" * 60)
    print("🚀 ESP32S3摔倒检测测试工具启动器 v2.0")
    print("=" * 60)
    
    # 显示网络信息
    local_ip = get_local_ip()
    print(f"🌐 本机IP地址: {local_ip}")
    print(f"📡 MQTT服务器地址: {local_ip}:1883")
    print(f"📋 MQTT主题: doll/imu_status")
    print()
    
    print("请选择启动方式:")
    print("1. 📡 启动增强MQTT服务器 (推荐)")
    print("2. 🔧 启动测试工具 (需要MQTT服务器已运行)")
    print("3. 📊 数据分析 (分析历史数据)")
    print("4. 🔄 同时启动服务器和测试工具")
    print()
    
    try:
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            print()
            print("=" * 50)
            print("📡 启动增强MQTT服务器")
            print("=" * 50)
            print("✨ 新功能:")
            print("  • 1秒打印一条数据统计")
            print("  • 实时转发数据给测试工具")
            print("  • 摔倒检测时显示详细警报")
            print("  • 支持多个测试工具同时连接")
            print()
            start_enhanced_mqtt_server()
            
        elif choice == '2':
            print()
            print("💡 提示: 请确保增强MQTT服务器已在运行")
            print(f"   如果没有，请先选择选项1启动MQTT服务器")
            print()
            input("按回车键继续启动测试工具...")
            start_test_tool()
            
        elif choice == '3':
            print()
            start_data_analysis()
            
        elif choice == '4':
            print()
            print("🔄 同时启动模式")
            print("💡 建议:")
            print("  1. 先在一个终端运行选项1启动MQTT服务器")
            print("  2. 再在另一个终端运行选项2启动测试工具")
            print()
            print("这样可以分别查看服务器和测试工具的日志")
            print()
            choice2 = input("是否继续在当前终端启动服务器? (y/n): ").strip().lower()
            if choice2 == 'y':
                start_enhanced_mqtt_server()
            
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
