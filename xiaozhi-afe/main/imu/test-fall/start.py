#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32S3摔倒检测测试工具启动脚本
"""

import os
import sys
import socket
import subprocess
import threading
import time

def get_local_ip():
    """获取本机IP地址"""
    # 硬编码返回实际的网络IP地址而不是虚拟网络接口地址
    return "*************"
    '''
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"
    '''

def check_dependencies():
    """检查并安装依赖"""
    print("🔍 检查Python依赖包...")
    
    required_packages = ['paho-mqtt', 'matplotlib', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 需要安装依赖包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败，请手动运行: pip install paho-mqtt matplotlib numpy")
            return False
    else:
        print("✅ 所有依赖包已安装")
    
    return True

def start_simple_mqtt_server():
    """启动简易MQTT服务器"""
    try:
        import paho.mqtt.broker.broker as broker
        print("🚀 启动专业MQTT代理服务器...")

        # 使用paho-mqtt的内置broker
        mqtt_broker = broker.Broker()
        mqtt_broker.start()

        local_ip = get_local_ip()
        print("=" * 50)
        print("🚀 ESP32S3摔倒检测 - MQTT代理服务器")
        print("=" * 50)
        print(f"✅ 服务器启动成功")
        print(f"📡 监听端口: 1883")
        print(f"🌐 客户端连接地址: {local_ip}:1883")
        print(f"📋 MQTT主题: doll/imu_status")
        print("-" * 50)
        print("⏹️  按Ctrl+C停止服务器")
        print()

        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️  正在停止服务器...")
            mqtt_broker.stop()
            print("✅ 服务器已停止")

    except ImportError:
        print("📦 paho-mqtt broker不可用，使用简易MQTT服务器...")
        start_basic_mqtt_server()

def start_basic_mqtt_server():
    """启动基础MQTT服务器"""
    import socket
    import threading
    import struct

    class BasicMQTTServer:
        def __init__(self, host='0.0.0.0', port=1883):
            self.host = host
            self.port = port
            self.running = True
            self.clients = {}
            self.subscriptions = {}

        def start(self):
            try:
                self.server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.server.bind((self.host, self.port))
                self.server.listen(10)

                local_ip = get_local_ip()

                print("=" * 50)
                print("🚀 ESP32S3摔倒检测 - 基础MQTT服务器")
                print("=" * 50)
                print(f"✅ 服务器启动成功")
                print(f"📡 监听端口: {self.port}")
                print(f"🌐 客户端连接地址: {local_ip}:{self.port}")
                print(f"📋 MQTT主题: doll/imu_status")
                print("-" * 50)
                print("⏹️  按Ctrl+C停止服务器")
                print("🔗 等待客户端连接...")
                print()

                while self.running:
                    try:
                        client_socket, addr = self.server.accept()
                        client_id = f"{addr[0]}:{addr[1]}"
                        self.clients[client_id] = client_socket
                        print(f"🔗 客户端连接: {addr[0]}:{addr[1]}")

                        client_thread = threading.Thread(
                            target=self.handle_client,
                            args=(client_socket, addr, client_id),
                            daemon=True
                        )
                        client_thread.start()

                    except socket.error:
                        if self.running:
                            print("❌ 接受连接失败")
                        break

            except Exception as e:
                print(f"❌ 服务器启动失败: {e}")
                if "Address already in use" in str(e):
                    print("💡 端口1883已被占用")
                return False

            return True

        def handle_client(self, client_socket, addr, client_id):
            """处理客户端连接"""
            try:
                while self.running:
                    data = client_socket.recv(4096)
                    if not data:
                        break

                    self.process_mqtt_packet(data, client_socket, addr, client_id)

            except Exception as e:
                print(f"⚠️  客户端 {addr[0]} 处理错误: {e}")
            finally:
                client_socket.close()
                if client_id in self.clients:
                    del self.clients[client_id]
                if client_id in self.subscriptions:
                    del self.subscriptions[client_id]
                print(f"🔌 客户端断开: {addr[0]}")

        def process_mqtt_packet(self, data, client_socket, addr, client_id):
            """处理MQTT数据包"""
            if len(data) < 2:
                return

            msg_type = (data[0] >> 4) & 0x0F

            if msg_type == 1:  # CONNECT
                # 发送CONNACK
                connack = bytes([0x20, 0x02, 0x00, 0x00])
                client_socket.send(connack)
                print(f"✅ {addr[0]} MQTT连接成功")

            elif msg_type == 8:  # SUBSCRIBE
                # 解析订阅主题
                try:
                    # 简单解析订阅包
                    if len(data) > 5:
                        topic_len = struct.unpack('>H', data[4:6])[0]
                        if len(data) >= 6 + topic_len:
                            topic = data[6:6+topic_len].decode('utf-8')
                            self.subscriptions[client_id] = topic
                            print(f"📋 {addr[0]} 订阅主题: {topic}")

                            # 发送SUBACK
                            packet_id = struct.unpack('>H', data[2:4])[0]
                            suback = bytes([0x90, 0x03]) + struct.pack('>H', packet_id) + bytes([0x00])
                            client_socket.send(suback)
                except:
                    pass

            elif msg_type == 3:  # PUBLISH
                # 解析发布消息
                try:
                    # 解析主题长度
                    topic_len = struct.unpack('>H', data[2:4])[0]
                    topic = data[4:4+topic_len].decode('utf-8')

                    # 检查是否有QoS字段
                    qos = (data[0] >> 1) & 0x03
                    payload_start = 4 + topic_len
                    if qos > 0:
                        payload_start += 2  # 跳过packet ID

                    payload = data[payload_start:].decode('utf-8', errors='ignore')

                    print(f"📨 收到发布: {addr[0]} -> {topic}")
                    print(f"   数据: {payload[:100]}{'...' if len(payload) > 100 else ''}")

                    # 转发给订阅了该主题的客户端
                    for sub_client_id, sub_topic in self.subscriptions.items():
                        if sub_topic == topic and sub_client_id != client_id:
                            if sub_client_id in self.clients:
                                try:
                                    # 重新构造PUBLISH包发送给订阅者
                                    topic_bytes = topic.encode('utf-8')
                                    payload_bytes = payload.encode('utf-8')

                                    # 构造新的PUBLISH包
                                    remaining_length = 2 + len(topic_bytes) + len(payload_bytes)

                                    # MQTT固定头部
                                    fixed_header = bytes([0x30])  # PUBLISH, QoS=0

                                    # 编码剩余长度
                                    length_bytes = []
                                    while remaining_length > 0:
                                        byte = remaining_length % 128
                                        remaining_length = remaining_length // 128
                                        if remaining_length > 0:
                                            byte |= 0x80
                                        length_bytes.append(byte)

                                    # 组装完整包
                                    packet = fixed_header + bytes(length_bytes)
                                    packet += struct.pack('>H', len(topic_bytes)) + topic_bytes
                                    packet += payload_bytes

                                    self.clients[sub_client_id].send(packet)
                                    print(f"📤 转发给: {sub_client_id}")
                                except Exception as e:
                                    print(f"⚠️  转发失败: {e}")
                except Exception as e:
                    print(f"📨 收到数据: {addr[0]} -> {len(data)} bytes, 解析错误: {e}")

            elif msg_type == 12:  # PINGREQ
                # 发送PINGRESP
                pingresp = bytes([0xD0, 0x00])
                client_socket.send(pingresp)

        def stop(self):
            """停止服务器"""
            self.running = False
            for client_socket in self.clients.values():
                try:
                    client_socket.close()
                except:
                    pass
            if hasattr(self, 'server'):
                self.server.close()

    # 启动服务器
    server = BasicMQTTServer()
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n⏹️  正在停止服务器...")
        server.stop()
        print("✅ 服务器已停止")

def start_test_tool():
    """启动测试工具"""
    print("🔧 启动摔倒检测测试工具...")
    try:
        subprocess.run([sys.executable, 'fall_detection_test.py'])
    except FileNotFoundError:
        print("❌ 找不到 fall_detection_test.py 文件")
    except Exception as e:
        print(f"❌ 启动测试工具失败: {e}")

def main():
    print("=" * 60)
    print("🚀 ESP32S3摔倒检测测试工具启动器")
    print("=" * 60)
    
    # 显示网络信息
    local_ip = get_local_ip()
    print(f"🌐 本机IP地址: {local_ip}")
    print(f"📡 MQTT服务器地址: {local_ip}:1883")
    print(f"📋 MQTT主题: doll/imu_status")
    print()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print()
    print("请选择启动方式:")
    print("1. 🔧 启动完整测试工具 (需要MQTT服务器已运行)")
    print("2. 🎯 启动简化测试工具 (推荐)")
    print("3. 📡 启动简易MQTT服务器")
    print("4. 📊 数据分析 (分析历史数据)")
    print()
    
    try:
        choice = input("请输入选择 (1-4): ").strip()

        if choice == '1':
            print()
            print("💡 提示: 请确保MQTT服务器已在运行")
            print(f"   如果没有，请先选择选项3启动MQTT服务器")
            print()
            input("按回车键继续启动完整测试工具...")
            start_test_tool()

        elif choice == '2':
            print()
            print("🎯 启动简化测试工具...")
            try:
                subprocess.run([sys.executable, 'simple_fall_test.py'])
            except FileNotFoundError:
                print("❌ 找不到 simple_fall_test.py 文件")
            except Exception as e:
                print(f"❌ 启动简化测试工具失败: {e}")

        elif choice == '3':
            print()
            start_simple_mqtt_server()

        elif choice == '4':
            print()
            print("📊 数据分析工具")
            print("请选择要分析的JSON日志文件:")
            
            # 列出当前目录的JSON文件
            json_files = [f for f in os.listdir('.') if f.endswith('.json') and f != 'config.json']
            if json_files:
                print("可用的日志文件:")
                for i, file in enumerate(json_files, 1):
                    print(f"  {i}. {file}")
                print()
                
                try:
                    file_choice = int(input("请输入文件编号: ")) - 1
                    if 0 <= file_choice < len(json_files):
                        selected_file = json_files[file_choice]
                        print(f"分析文件: {selected_file}")
                        subprocess.run([sys.executable, 'analyze_data.py', selected_file])
                    else:
                        print("❌ 无效的文件编号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            else:
                print("❌ 当前目录没有找到JSON日志文件")
                print("💡 请先进行测试并保存数据")
                
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
