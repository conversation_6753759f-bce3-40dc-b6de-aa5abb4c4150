#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摔倒检测数据分析脚本
分析测试日志，生成统计报告和图表
"""

import json
import sys
import os
from datetime import datetime
import matplotlib.pyplot as plt
import numpy as np

class FallDataAnalyzer:
    def __init__(self, log_file):
        self.log_file = log_file
        self.data = None
        self.load_data()
        
    def load_data(self):
        """加载测试数据"""
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 成功加载数据文件: {self.log_file}")
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            sys.exit(1)
            
    def analyze_basic_stats(self):
        """基础统计分析"""
        print("\n📊 基础统计信息:")
        print("-" * 40)
        
        test_start = datetime.fromisoformat(self.data['test_start_time'])
        test_end = datetime.fromisoformat(self.data['test_end_time'])
        duration = test_end - test_start
        
        print(f"测试开始时间: {test_start.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试结束时间: {test_end.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试持续时间: {duration}")
        print(f"摔倒检测次数: {self.data['fall_count']}")
        print(f"数据采样点数: {self.data['total_samples']}")
        
        if duration.total_seconds() > 0:
            sample_rate = self.data['total_samples'] / duration.total_seconds()
            print(f"平均采样率: {sample_rate:.2f} Hz")
            
    def analyze_fall_events(self):
        """分析摔倒事件"""
        print("\n🚨 摔倒事件分析:")
        print("-" * 40)
        
        fall_events = []
        for entry in self.data['data_log']:
            if entry['data'].get('fall_state') == 3:
                fall_events.append(entry)
                
        print(f"摔倒事件总数: {len(fall_events)}")
        
        if fall_events:
            print("\n摔倒事件详情:")
            for i, event in enumerate(fall_events, 1):
                timestamp = event['timestamp']
                data = event['data']
                acc_mag = (data.get('ax', 0)**2 + data.get('ay', 0)**2 + data.get('az', 0)**2)**0.5
                gyro_mag = (data.get('gx', 0)**2 + data.get('gy', 0)**2 + data.get('gz', 0)**2)**0.5
                
                print(f"  事件 {i}: {timestamp}")
                print(f"    加速度模长: {acc_mag:.3f}g")
                print(f"    角速度模长: {gyro_mag:.3f}°/s")
                print(f"    姿态角: X={data.get('angle_x', 0):.1f}° Y={data.get('angle_y', 0):.1f}° Z={data.get('angle_z', 0):.1f}°")
                
    def analyze_motion_distribution(self):
        """分析运动状态分布"""
        print("\n🏃 运动状态分布:")
        print("-" * 40)
        
        motion_counts = {0: 0, 1: 0, 2: 0, 3: 0}
        motion_names = {0: "静止", 1: "轻微运动", 2: "中等运动", 3: "剧烈运动"}
        
        for entry in self.data['data_log']:
            motion = entry['data'].get('imu_type', 0)
            motion_counts[motion] += 1
            
        total = sum(motion_counts.values())
        for state, count in motion_counts.items():
            percentage = (count / total * 100) if total > 0 else 0
            print(f"  {motion_names[state]}: {count} ({percentage:.1f}%)")
            
    def plot_imu_data(self):
        """绘制IMU数据图表"""
        print("\n📈 生成数据图表...")
        
        # 提取时间序列数据
        timestamps = []
        acc_x, acc_y, acc_z = [], [], []
        gyr_x, gyr_y, gyr_z = [], [], []
        fall_states = []
        
        for entry in self.data['data_log']:
            timestamps.append(entry['timestamp'])
            data = entry['data']
            acc_x.append(data.get('ax', 0))
            acc_y.append(data.get('ay', 0))
            acc_z.append(data.get('az', 0))
            gyr_x.append(data.get('gx', 0))
            gyr_y.append(data.get('gy', 0))
            gyr_z.append(data.get('gz', 0))
            fall_states.append(data.get('fall_state', 0))
            
        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        fig.suptitle('ESP32S3 IMU数据分析', fontsize=16)
        
        # 加速度图
        axes[0].plot(acc_x, label='X轴', alpha=0.7)
        axes[0].plot(acc_y, label='Y轴', alpha=0.7)
        axes[0].plot(acc_z, label='Z轴', alpha=0.7)
        axes[0].set_ylabel('加速度 (g)')
        axes[0].set_title('加速度数据')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 角速度图
        axes[1].plot(gyr_x, label='X轴', alpha=0.7)
        axes[1].plot(gyr_y, label='Y轴', alpha=0.7)
        axes[1].plot(gyr_z, label='Z轴', alpha=0.7)
        axes[1].set_ylabel('角速度 (°/s)')
        axes[1].set_title('角速度数据')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # 摔倒状态图
        axes[2].plot(fall_states, 'r-', linewidth=2, label='摔倒状态')
        axes[2].set_ylabel('摔倒状态')
        axes[2].set_xlabel('采样点')
        axes[2].set_title('摔倒检测状态')
        axes[2].set_ylim(-0.5, 3.5)
        axes[2].set_yticks([0, 1, 2, 3])
        axes[2].set_yticklabels(['正常', '冲击', '确认中', '摔倒'])
        axes[2].grid(True, alpha=0.3)
        
        # 标记摔倒事件
        fall_indices = [i for i, state in enumerate(fall_states) if state == 3]
        for idx in fall_indices:
            for ax in axes:
                ax.axvline(x=idx, color='red', linestyle='--', alpha=0.5)
                
        plt.tight_layout()
        
        # 保存图表
        chart_file = self.log_file.replace('.json', '_chart.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {chart_file}")
        
        # 显示图表
        plt.show()
        
    def generate_report(self):
        """生成分析报告"""
        report_file = self.log_file.replace('.json', '_report.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("ESP32S3 摔倒检测测试报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 基础信息
            test_start = datetime.fromisoformat(self.data['test_start_time'])
            test_end = datetime.fromisoformat(self.data['test_end_time'])
            duration = test_end - test_start
            
            f.write(f"测试时间: {test_start.strftime('%Y-%m-%d %H:%M:%S')} - {test_end.strftime('%H:%M:%S')}\n")
            f.write(f"测试时长: {duration}\n")
            f.write(f"摔倒次数: {self.data['fall_count']}\n")
            f.write(f"采样点数: {self.data['total_samples']}\n\n")
            
            # 摔倒事件
            fall_events = [e for e in self.data['data_log'] if e['data'].get('fall_state') == 3]
            f.write(f"摔倒事件详情 ({len(fall_events)}次):\n")
            f.write("-" * 30 + "\n")
            
            for i, event in enumerate(fall_events, 1):
                data = event['data']
                acc_mag = (data.get('ax', 0)**2 + data.get('ay', 0)**2 + data.get('az', 0)**2)**0.5
                gyro_mag = (data.get('gx', 0)**2 + data.get('gy', 0)**2 + data.get('gz', 0)**2)**0.5
                
                f.write(f"{i}. 时间: {event['timestamp']}\n")
                f.write(f"   加速度: {acc_mag:.3f}g, 角速度: {gyro_mag:.3f}°/s\n")
                f.write(f"   姿态: X={data.get('angle_x', 0):.1f}° Y={data.get('angle_y', 0):.1f}° Z={data.get('angle_z', 0):.1f}°\n\n")
                
        print(f"📄 报告已保存: {report_file}")
        
    def run_analysis(self):
        """运行完整分析"""
        print("🔍 开始分析摔倒检测数据...")
        
        self.analyze_basic_stats()
        self.analyze_fall_events()
        self.analyze_motion_distribution()
        
        try:
            self.plot_imu_data()
        except ImportError:
            print("⚠️  matplotlib未安装，跳过图表生成")
        except Exception as e:
            print(f"⚠️  图表生成失败: {e}")
            
        self.generate_report()
        print("\n✅ 分析完成！")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python analyze_data.py <日志文件.json>")
        sys.exit(1)
        
    log_file = sys.argv[1]
    if not os.path.exists(log_file):
        print(f"❌ 文件不存在: {log_file}")
        sys.exit(1)
        
    analyzer = FallDataAnalyzer(log_file)
    analyzer.run_analysis()
