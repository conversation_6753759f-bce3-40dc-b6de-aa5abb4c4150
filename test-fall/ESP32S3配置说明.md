# ESP32S3摔倒检测测试配置说明

## 🎯 已完成的代码修改

### ✅ MQTT服务器配置
已将MQTT服务器地址硬编码为您的电脑IP：
```cpp
// 在 xiaozhi-afe/main/protocols/mqtt_protocol.cc 第39行
auto endpoint = std::string("mqtt://*************:1883");
```

### ✅ MQTT数据发送
已启用IMU数据发送到测试工具：
```cpp
// 在 xiaozhi-afe/main/protocols/mqtt_protocol.cc 第425行
mqtt_->Publish(imu_topic, message);  // 已取消注释
```

### ✅ 数据主题
IMU数据发送到主题：`doll/imu_status`（与测试工具匹配）

## 🌐 WiFi配置步骤

ESP32S3需要连接到与您电脑相同的WiFi网络（192.168.2.x网段）。

### 方法1：通过WiFi配置模式（推荐）

1. **进入配置模式**：
   - 首次启动ESP32S3时，如果没有保存的WiFi配置，会自动进入配置模式
   - 或者在启动时按住BOOT按钮进入配置模式

2. **连接配置热点**：
   - ESP32S3会创建一个名为 `Xiaozhi-xxxxxx` 的WiFi热点
   - 用手机或电脑连接这个热点

3. **配置WiFi**：
   - 浏览器访问：`http://***********`
   - 选择您的WiFi网络
   - 输入WiFi密码
   - 点击连接

4. **确认连接**：
   - ESP32S3会自动连接到您的WiFi
   - 获得192.168.2.xxx的IP地址

### 方法2：通过串口监控确认

连接ESP32S3的串口，查看启动日志：
```
I (xxxx) WifiStation: Connected to WiFi: YourWiFiName
I (xxxx) WifiStation: IP Address: 192.168.2.xxx
I (xxxx) MqttProtocol: Using hardcoded MQTT endpoint for testing: mqtt://*************:1883
I (xxxx) MqttProtocol: MQTT connected
```

## 🚀 测试步骤

### 1. 启动测试环境

在您的电脑上：
```bash
# 进入测试目录
cd test-fall

# 启动测试工具
python start.py
# 选择 2 - 启动MQTT服务器
# 再选择 1 - 启动测试工具
```

### 2. 启动ESP32S3

- 确保ESP32S3已连接到正确的WiFi网络
- 重启ESP32S3设备
- 观察串口日志确认MQTT连接成功

### 3. 验证连接

在测试工具中应该看到：
- 连接状态显示"已连接"
- 实时接收IMU数据
- 数据速率显示约50Hz

## 🔍 故障排除

### 问题1：ESP32S3无法连接WiFi
**解决方案**：
- 确认WiFi密码正确
- 确认WiFi网络是2.4GHz（ESP32S3不支持5GHz）
- 重新进入WiFi配置模式

### 问题2：MQTT连接失败
**解决方案**：
- 确认ESP32S3和电脑在同一网段（192.168.2.x）
- 确认电脑防火墙允许1883端口
- 确认测试工具的MQTT服务器正在运行

### 问题3：测试工具收不到数据
**解决方案**：
- 检查ESP32S3串口日志确认数据发送
- 确认MQTT主题匹配（doll/imu_status）
- 重启测试工具和ESP32S3

## 📋 验证清单

- [ ] ESP32S3连接到正确的WiFi网络
- [ ] ESP32S3获得192.168.2.xxx IP地址
- [ ] 电脑上MQTT服务器运行在*************:1883
- [ ] ESP32S3串口显示MQTT连接成功
- [ ] 测试工具显示"已连接"状态
- [ ] 测试工具实时接收IMU数据
- [ ] 摔倒检测功能正常工作

## 🎉 完成！

配置完成后，您就可以开始进行摔倒检测算法的测试了！

ESP32S3会以50Hz的频率发送IMU数据到您的测试工具，包括：
- 加速度数据 (ax, ay, az)
- 角速度数据 (gx, gy, gz)  
- 姿态角数据 (angle_x, angle_y, angle_z)
- 运动状态 (imu_type)
- 摔倒检测状态 (fall_state)
