#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32S3摔倒检测测试工具启动脚本
"""

import os
import sys
import socket
import subprocess
import threading
import time

def get_local_ip():
    """获取本机IP地址"""
    # 硬编码返回实际的网络IP地址而不是虚拟网络接口地址
    return "*************"
    '''
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"
    '''

def check_dependencies():
    """检查并安装依赖"""
    print("🔍 检查Python依赖包...")
    
    required_packages = ['paho-mqtt', 'matplotlib', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 需要安装依赖包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败，请手动运行: pip install paho-mqtt matplotlib numpy")
            return False
    else:
        print("✅ 所有依赖包已安装")
    
    return True

def start_simple_mqtt_server():
    """启动简易MQTT服务器"""
    import socket
    import threading
    import json
    import time
    import paho.mqtt.client as mqtt

    class SimpleMQTTServer:
        def __init__(self, host='0.0.0.0', port=1883):
            self.host = host
            self.port = port
            self.running = True
            self.last_print_time = 0
            self.data_count = 0
            self.fall_count = 0

            # 创建MQTT客户端用于转发数据
            self.mqtt_client = mqtt.Client()
            self.mqtt_client.on_connect = self.on_mqtt_connect
            self.mqtt_client.on_disconnect = self.on_mqtt_disconnect

        def on_mqtt_connect(self, client, userdata, flags, rc):
            if rc == 0:
                print("✅ MQTT转发客户端连接成功")
            else:
                print(f"❌ MQTT转发客户端连接失败: {rc}")

        def on_mqtt_disconnect(self, client, userdata, rc):
            print("⚠️ MQTT转发客户端断开连接")

        def start(self):
            try:
                # 启动内置MQTT broker
                self.start_internal_broker()

                self.server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.server.bind((self.host, self.port))
                self.server.listen(5)

                local_ip = get_local_ip()

                print("=" * 50)
                print("🚀 ESP32S3摔倒检测 - 增强MQTT服务器")
                print("=" * 50)
                print(f"✅ 服务器启动成功")
                print(f"📡 监听端口: {self.port}")
                print(f"🌐 客户端连接地址: {local_ip}:{self.port}")
                print(f"📋 MQTT主题: doll/imu_status")
                print(f"🔄 数据转发: 启用 (内置broker端口: 1884)")
                print("-" * 50)
                print("💡 ESP32S3配置代码:")
                print(f'   const char* mqtt_server = "{local_ip}";')
                print(f'   const int mqtt_port = {self.port};')
                print("-" * 50)
                print("⏹️  按Ctrl+C停止服务器")
                print("🔗 等待ESP32S3连接...")
                print()

                while self.running:
                    try:
                        client_socket, addr = self.server.accept()
                        print(f"🔗 设备连接: {addr[0]}:{addr[1]}")

                        client_thread = threading.Thread(
                            target=self.handle_client,
                            args=(client_socket, addr),
                            daemon=True
                        )
                        client_thread.start()

                    except socket.error:
                        if self.running:
                            print("❌ 接受连接失败")
                        break

            except Exception as e:
                print(f"❌ 服务器启动失败: {e}")
                if "Address already in use" in str(e):
                    print("💡 端口1883已被占用，可能已有MQTT服务器在运行")
                return False

            return True

        def start_internal_broker(self):
            """启动内置MQTT broker"""
            try:
                # 连接到本地MQTT broker (如果有的话)
                # 如果没有，我们将创建一个简单的转发机制
                self.mqtt_client.connect("localhost", 1884, 60)
                self.mqtt_client.loop_start()
            except:
                print("💡 未找到本地MQTT broker，将使用直接转发模式")
            
        def handle_client(self, client_socket, addr):
            """处理客户端连接"""
            try:
                while self.running:
                    data = client_socket.recv(1024)
                    if not data:
                        break

                    # 简单的MQTT协议处理
                    if data[0] == 0x10:  # CONNECT
                        # 发送CONNACK
                        connack = bytes([0x20, 0x02, 0x00, 0x00])
                        client_socket.send(connack)
                        print(f"✅ {addr[0]} MQTT连接成功")
                    elif (data[0] >> 4) == 3:  # PUBLISH
                        self.data_count += 1
                        current_time = time.time()

                        # 解析MQTT PUBLISH消息
                        try:
                            # 简单解析MQTT PUBLISH包
                            # 跳过固定头部，获取payload
                            remaining_length = data[1]
                            topic_length = (data[2] << 8) | data[3]
                            topic_start = 4
                            topic_end = topic_start + topic_length
                            payload_start = topic_end

                            if payload_start < len(data):
                                payload = data[payload_start:].decode('utf-8', errors='ignore')

                                # 尝试解析JSON
                                try:
                                    json_data = json.loads(payload)
                                    self.process_imu_data(json_data, addr[0], current_time)
                                except json.JSONDecodeError:
                                    pass

                        except Exception as parse_error:
                            pass

                        # 1秒打印一条数据
                        if current_time - self.last_print_time >= 1.0:
                            print(f"📊 数据统计: 收到 {self.data_count} 条IMU数据 | 摔倒次数: {self.fall_count}")
                            self.last_print_time = current_time
                            self.data_count = 0

                        # 发送PUBACK (如果需要)
                        if len(data) >= 4:
                            qos = (data[0] >> 1) & 0x03
                            if qos > 0:
                                puback = bytes([0x40, 0x02, data[2], data[3]])
                                client_socket.send(puback)
                    elif data[0] == 0xC0:  # PINGREQ
                        # 发送PINGRESP
                        pingresp = bytes([0xD0, 0x00])
                        client_socket.send(pingresp)

            except Exception as e:
                print(f"⚠️  客户端处理错误: {e}")
            finally:
                client_socket.close()
                print(f"🔌 设备断开: {addr[0]}")

        def process_imu_data(self, json_data, client_ip, timestamp):
            """处理IMU数据"""
            try:
                # 检查摔倒状态
                fall_state = json_data.get('fall_state', 0)
                if fall_state == 3:  # 摔倒确认
                    self.fall_count += 1
                    print("🚨" * 10)
                    print(f"🚨 摔倒检测！第 {self.fall_count} 次")
                    print(f"🚨 设备: {client_ip}")
                    print(f"🚨 时间: {time.strftime('%H:%M:%S', time.localtime(timestamp))}")
                    print(f"🚨 加速度: X={json_data.get('ax', 0):.2f} Y={json_data.get('ay', 0):.2f} Z={json_data.get('az', 0):.2f}")
                    print(f"🚨 角速度: X={json_data.get('gx', 0):.2f} Y={json_data.get('gy', 0):.2f} Z={json_data.get('gz', 0):.2f}")
                    print("🚨" * 10)

                # 转发数据到MQTT broker (实时转发)
                self.forward_to_mqtt(json_data)

            except Exception as e:
                print(f"⚠️ 数据处理错误: {e}")

        def forward_to_mqtt(self, json_data):
            """转发数据到MQTT broker"""
            try:
                if self.mqtt_client and self.mqtt_client.is_connected():
                    message = json.dumps(json_data)
                    self.mqtt_client.publish("doll/imu_status", message)
            except Exception as e:
                # 静默处理转发错误，避免影响主要功能
                pass
                
        def stop(self):
            """停止服务器"""
            self.running = False
            if hasattr(self, 'server'):
                self.server.close()
    
    # 启动服务器
    server = SimpleMQTTServer()
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n⏹️  正在停止服务器...")
        server.stop()
        print("✅ 服务器已停止")

def start_test_tool():
    """启动测试工具"""
    print("🔧 启动摔倒检测测试工具...")
    try:
        subprocess.run([sys.executable, 'fall_detection_test.py'])
    except FileNotFoundError:
        print("❌ 找不到 fall_detection_test.py 文件")
    except Exception as e:
        print(f"❌ 启动测试工具失败: {e}")

def main():
    print("=" * 60)
    print("🚀 ESP32S3摔倒检测测试工具启动器")
    print("=" * 60)
    
    # 显示网络信息
    local_ip = get_local_ip()
    print(f"🌐 本机IP地址: {local_ip}")
    print(f"📡 MQTT服务器地址: {local_ip}:1883")
    print(f"📋 MQTT主题: doll/imu_status")
    print()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print()
    print("请选择启动方式:")
    print("1. 🔧 启动测试工具 (需要MQTT服务器已运行)")
    print("2. 📡 启动简易MQTT服务器")
    print("3. 📊 数据分析 (分析历史数据)")
    print()
    
    try:
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == '1':
            print()
            print("💡 提示: 请确保MQTT服务器已在运行")
            print(f"   如果没有，请先选择选项2启动MQTT服务器")
            print()
            input("按回车键继续启动测试工具...")
            start_test_tool()
            
        elif choice == '2':
            print()
            start_simple_mqtt_server()
            
        elif choice == '3':
            print()
            print("📊 数据分析工具")
            print("请选择要分析的JSON日志文件:")
            
            # 列出当前目录的JSON文件
            json_files = [f for f in os.listdir('.') if f.endswith('.json') and f != 'config.json']
            if json_files:
                print("可用的日志文件:")
                for i, file in enumerate(json_files, 1):
                    print(f"  {i}. {file}")
                print()
                
                try:
                    file_choice = int(input("请输入文件编号: ")) - 1
                    if 0 <= file_choice < len(json_files):
                        selected_file = json_files[file_choice]
                        print(f"分析文件: {selected_file}")
                        subprocess.run([sys.executable, 'analyze_data.py', selected_file])
                    else:
                        print("❌ 无效的文件编号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            else:
                print("❌ 当前目录没有找到JSON日志文件")
                print("💡 请先进行测试并保存数据")
                
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
