#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32S3摔倒检测测试工具启动脚本
"""

import os
import sys
import socket
import subprocess
import threading
import time

def get_local_ip():
    """获取本机IP地址"""
    # 硬编码返回实际的网络IP地址而不是虚拟网络接口地址
    return "*************"
    '''
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"
    '''

def check_dependencies():
    """检查并安装依赖"""
    print("🔍 检查Python依赖包...")
    
    required_packages = ['paho-mqtt', 'matplotlib', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 需要安装依赖包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败，请手动运行: pip install paho-mqtt matplotlib numpy")
            return False
    else:
        print("✅ 所有依赖包已安装")
    
    return True

def start_simple_mqtt_server():
    """启动简易MQTT服务器"""
    import socket
    import threading
    
    class SimpleMQTTServer:
        def __init__(self, host='0.0.0.0', port=1883):
            self.host = host
            self.port = port
            self.running = True
            
        def start(self):
            try:
                self.server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.server.bind((self.host, self.port))
                self.server.listen(5)
                
                local_ip = get_local_ip()
                
                print("=" * 50)
                print("🚀 ESP32S3摔倒检测 - 简易MQTT服务器")
                print("=" * 50)
                print(f"✅ 服务器启动成功")
                print(f"📡 监听端口: {self.port}")
                print(f"🌐 客户端连接地址: {local_ip}:{self.port}")
                print(f"📋 MQTT主题: doll/imu_status")
                print("-" * 50)
                print("💡 ESP32S3配置代码:")
                print(f'   const char* mqtt_server = "{local_ip}";')
                print(f'   const int mqtt_port = {self.port};')
                print("-" * 50)
                print("⏹️  按Ctrl+C停止服务器")
                print("🔗 等待ESP32S3连接...")
                print()
                
                while self.running:
                    try:
                        client_socket, addr = self.server.accept()
                        print(f"🔗 设备连接: {addr[0]}:{addr[1]}")
                        
                        client_thread = threading.Thread(
                            target=self.handle_client,
                            args=(client_socket, addr),
                            daemon=True
                        )
                        client_thread.start()
                        
                    except socket.error:
                        if self.running:
                            print("❌ 接受连接失败")
                        break
                        
            except Exception as e:
                print(f"❌ 服务器启动失败: {e}")
                if "Address already in use" in str(e):
                    print("💡 端口1883已被占用，可能已有MQTT服务器在运行")
                return False
                
            return True
            
        def handle_client(self, client_socket, addr):
            """处理客户端连接"""
            try:
                while self.running:
                    data = client_socket.recv(1024)
                    if not data:
                        break
                        
                    # 简单的MQTT协议处理
                    if data[0] == 0x10:  # CONNECT
                        # 发送CONNACK
                        connack = bytes([0x20, 0x02, 0x00, 0x00])
                        client_socket.send(connack)
                        print(f"✅ {addr[0]} MQTT连接成功")
                    elif (data[0] >> 4) == 3:  # PUBLISH
                        print(f"📨 收到IMU数据: {addr[0]} -> {len(data)} bytes")
                        # 发送PUBACK (如果需要)
                        if len(data) >= 4:
                            qos = (data[0] >> 1) & 0x03
                            if qos > 0:
                                puback = bytes([0x40, 0x02, data[2], data[3]])
                                client_socket.send(puback)
                    elif data[0] == 0xC0:  # PINGREQ
                        # 发送PINGRESP
                        pingresp = bytes([0xD0, 0x00])
                        client_socket.send(pingresp)
                        
            except Exception as e:
                print(f"⚠️  客户端处理错误: {e}")
            finally:
                client_socket.close()
                print(f"🔌 设备断开: {addr[0]}")
                
        def stop(self):
            """停止服务器"""
            self.running = False
            if hasattr(self, 'server'):
                self.server.close()
    
    # 启动服务器
    server = SimpleMQTTServer()
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n⏹️  正在停止服务器...")
        server.stop()
        print("✅ 服务器已停止")

def start_test_tool():
    """启动测试工具"""
    print("🔧 启动摔倒检测测试工具...")
    try:
        subprocess.run([sys.executable, 'fall_detection_test.py'])
    except FileNotFoundError:
        print("❌ 找不到 fall_detection_test.py 文件")
    except Exception as e:
        print(f"❌ 启动测试工具失败: {e}")

def main():
    print("=" * 60)
    print("🚀 ESP32S3摔倒检测测试工具启动器")
    print("=" * 60)
    
    # 显示网络信息
    local_ip = get_local_ip()
    print(f"🌐 本机IP地址: {local_ip}")
    print(f"📡 MQTT服务器地址: {local_ip}:1883")
    print(f"📋 MQTT主题: doll/imu_status")
    print()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print()
    print("请选择启动方式:")
    print("1. 🔧 启动测试工具 (需要MQTT服务器已运行)")
    print("2. 📡 启动简易MQTT服务器")
    print("3. 📊 数据分析 (分析历史数据)")
    print()
    
    try:
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == '1':
            print()
            print("💡 提示: 请确保MQTT服务器已在运行")
            print(f"   如果没有，请先选择选项2启动MQTT服务器")
            print()
            input("按回车键继续启动测试工具...")
            start_test_tool()
            
        elif choice == '2':
            print()
            start_simple_mqtt_server()
            
        elif choice == '3':
            print()
            print("📊 数据分析工具")
            print("请选择要分析的JSON日志文件:")
            
            # 列出当前目录的JSON文件
            json_files = [f for f in os.listdir('.') if f.endswith('.json') and f != 'config.json']
            if json_files:
                print("可用的日志文件:")
                for i, file in enumerate(json_files, 1):
                    print(f"  {i}. {file}")
                print()
                
                try:
                    file_choice = int(input("请输入文件编号: ")) - 1
                    if 0 <= file_choice < len(json_files):
                        selected_file = json_files[file_choice]
                        print(f"分析文件: {selected_file}")
                        subprocess.run([sys.executable, 'analyze_data.py', selected_file])
                    else:
                        print("❌ 无效的文件编号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            else:
                print("❌ 当前目录没有找到JSON日志文件")
                print("💡 请先进行测试并保存数据")
                
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
