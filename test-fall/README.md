# ESP32S3 摔倒检测测试工具

专为ESP32S3摔倒检测算法设计的测试工具，提供实时监控、数据分析和图表显示功能。

## 📁 核心文件（只有4个）

- `fall_detection_test.py` - **主测试工具**（图形界面）
- `analyze_data.py` - 数据分析脚本
- `config.json` - 配置文件
- `start.bat` - Windows启动脚本

## 🌟 主要功能

- **实时监控**：IMU数据、摔倒状态、运动状态
- **数据图表**：实时图表显示，支持导出
- **统计分析**：摔倒次数、准确率、事件列表
- **数据管理**：JSON/CSV格式保存和加载

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖包
pip install -r requirements.txt
```

### 2. 配置MQTT连接

编辑 `config.json` 文件，修改MQTT服务器配置：

```json
{
  "mqtt": {
    "broker": "your_mqtt_server_ip",  // 修改为您的MQTT服务器IP
    "port": 1883,
    "topic": "doll/imu_status"        // 确认主题名称
  }
}
```

### 3. 运行测试

#### 方式1: 双击启动（推荐）
```bash
# 双击运行
启动.bat
```

#### 方式2: 命令行启动
```bash
python start.py
```

#### 方式3: 直接启动测试工具
```bash
python fall_detection_test.py
```

## 🎯 详细测试指南

### 🚀 快速开始
1. **启动工具**：运行 `start_test.bat` 或 `python fall_detection_test.py`
2. **配置连接**：在"测试控制"标签页设置MQTT服务器信息
3. **开始测试**：点击"开始测试"按钮
4. **确认连接**：查看连接状态显示为"已连接"

### 📱 界面功能说明

#### 实时监控标签页
- **连接状态**：显示MQTT连接状态和数据速率
- **IMU原始数据**：实时显示加速度、角速度、姿态角
- **检测状态**：运动状态和摔倒状态的彩色显示
- **计算数据**：加速度和角速度模长
- **测试统计**：测试时长、摔倒次数、误报次数、样本数
- **实时日志**：详细的事件日志记录

#### 数据图表标签页
- **实时图表**：4个子图显示不同类型的数据
- **显示控制**：可选择显示/隐藏不同类型的数据
- **图表操作**：保存图表、清除数据功能
- **摔倒标记**：自动在图表中标记摔倒事件

#### 统计分析标签页
- **基础统计**：测试概览和性能指标
- **摔倒事件**：详细的摔倒事件统计和准确率
- **分布统计**：运动状态和摔倒状态的分布
- **事件列表**：表格形式显示所有摔倒事件详情

#### 测试控制标签页
- **连接控制**：MQTT服务器配置和连接管理
- **测试控制**：开始/停止/暂停/重置功能
- **手动操作**：标记误报、手动触发、测试声音
- **设置选项**：自动保存、声音提示、图表更新开关
- **数据管理**：保存/导出/加载/清除数据
- **高级设置**：缓冲区大小、更新间隔等参数

### 🧪 系统性测试流程

#### 第一阶段：基线测试
1. **静止测试**：设备平放，观察5分钟
   - 记录静止状态下的数据范围
   - 确认运动状态显示为"静止"
   - 确认摔倒状态显示为"正常"

2. **轻微运动测试**：轻轻晃动设备
   - 观察运动状态变化
   - 确认不会误触发摔倒检测

#### 第二阶段：阈值测试
1. **逐步增加运动强度**：
   - 轻微晃动 → 中等晃动 → 剧烈晃动
   - 观察运动状态变化阈值
   - 记录触发冲击检测的动作强度

2. **快速移动测试**：
   - 快速移动但不摔倒
   - 观察是否触发冲击检测
   - 验证是否能正确回到正常状态

#### 第三阶段：摔倒模拟测试
1. **安全摔倒测试**：
   - 在软垫上进行测试
   - 快速抛掷设备到软垫
   - 快速旋转后静止
   - 模拟不同角度的摔倒

2. **观察完整检测流程**：
   - 正常 → 冲击检测 → 确认中 → 摔倒确认
   - 记录每个阶段的持续时间
   - 验证声音和视觉提示

#### 第四阶段：误报测试
1. **日常活动模拟**：
   - 模拟走路、跑步动作
   - 上下楼梯动作
   - 坐下、站起动作

2. **误报处理**：
   - 如发生误报，点击"标记误报"
   - 记录误报的具体动作
   - 分析误报原因

### 📊 数据分析和优化

#### 实时监控指标
- **数据速率**：应保持在45-55Hz范围内
- **加速度模长**：静止时约1g，运动时变化
- **角速度模长**：静止时接近0°/s
- **摔倒状态变化**：观察状态转换的合理性

#### 性能评估指标
- **检测准确率** = 真实摔倒次数 / (真实摔倒次数 + 误报次数)
- **响应时间**：从冲击到确认摔倒的时间
- **稳定性**：长时间运行的稳定性

#### 参数调优建议
根据测试结果调整ESP32S3代码中的参数：

```cpp
// 如果检测过于敏感（误报多）
config.acc_threshold = 3.0f;        // 提高冲击阈值
config.gyro_threshold = 180.0f;     // 提高角速度阈值
config.stable_time_ms = 1200;       // 延长确认时间

// 如果检测不够敏感（漏报多）
config.acc_threshold = 2.0f;        // 降低冲击阈值
config.gyro_threshold = 120.0f;     // 降低角速度阈值
config.posture_angle_threshold = 45.0f; // 降低角度阈值
config.stable_time_ms = 800;        // 缩短确认时间
```

## 📊 数据分析

### 保存测试数据
测试完成后点击"保存日志"，会生成JSON格式的测试数据文件。

### 分析测试结果
```bash
python analyze_data.py fall_test_log_20231201_143022.json
```

分析脚本会生成：
- 基础统计信息
- 摔倒事件详情
- 运动状态分布
- 数据图表（PNG格式）
- 分析报告（TXT格式）

## 🔧 参数调优

如果检测效果不理想，可以在ESP32S3代码中调整参数：

```cpp
// 在QMI8658初始化后调整
fall_detection_config_t config;
config.acc_threshold = 2.0f;        // 降低冲击阈值（更敏感）
config.gyro_threshold = 120.0f;     // 降低角速度阈值
config.posture_angle_threshold = 45.0f; // 降低角度阈值
config.stable_time_ms = 800;        // 缩短确认时间
imu_sensor->SetFallDetectionConfig(config);
```

### 参数说明
- `acc_threshold`: 加速度冲击阈值（g），默认2.5g
- `gyro_threshold`: 角速度阈值（°/s），默认150°/s
- `posture_angle_threshold`: 姿态角变化阈值（°），默认60°
- `stable_time_ms`: 确认时间（毫秒），默认1000ms

## 🚨 测试注意事项

### 安全提醒
- 测试时注意设备安全，避免损坏
- 在软垫或草地上进行摔倒测试
- 避免在硬地面上测试

### 环境要求
- 确保MQTT服务器可访问
- Windows环境下运行（使用winsound）
- Python 3.7+

### 常见问题

**Q: 连接不上MQTT服务器**
A: 检查config.json中的服务器IP和端口，确认网络连通性

**Q: 没有收到数据**
A: 确认ESP32S3设备正在发送数据，检查MQTT主题名称

**Q: 摔倒检测不敏感**
A: 降低acc_threshold和gyro_threshold参数值

**Q: 误报太多**
A: 提高阈值参数，增加stable_time_ms

## 📈 测试建议

### 系统性测试
1. **基线测试**：记录静止状态下的数据范围
2. **阈值测试**：逐步增加运动强度，找到触发点
3. **误报测试**：日常活动（走路、跑步、上下楼）
4. **真实摔倒**：模拟各种摔倒场景

### 数据收集
- 每种测试至少进行10次
- 记录环境条件（温度、湿度等）
- 保存所有测试数据用于算法优化

## 📞 技术支持

如有问题请检查：
1. Python依赖是否正确安装
2. MQTT服务器配置是否正确
3. ESP32S3设备是否正常工作
4. 网络连接是否稳定

祝测试顺利！🎉
